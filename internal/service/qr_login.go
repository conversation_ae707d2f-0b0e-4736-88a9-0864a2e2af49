package service

import (
	"context"
	"fmt"
	"strings"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
)

// QRLoginService 负责管理扫码登录UUID在Redis中的存储与失效
type QRLoginService struct{}

var (
	_qrLoginService *QRLoginService
)

func SingletonQRLoginService() *QRLoginService {
	if _qrLoginService == nil {
		_qrLoginService = &QRLoginService{}
	}
	return _qrLoginService
}

// SetActiveUUID 为指定userID设置当前激活UUID，并写入带TTL的UUID->userID键
// 若该用户已有旧UUID，会被覆盖
func (s *QRLoginService) SetActiveUUID(ctx context.Context, userID string, ctxName string, uuid string) error {
	// activeKey 以 userID + context 作为维度
	activeKey := fmt.Sprintf(constants.RedisQRLoginActiveKey, userID+":"+ctxName)
	uuidKey := fmt.Sprintf(constants.RedisQRLoginUUIDKey, uuid)

	// 写入uuid -> user_id，设置5分钟TTL
	if err := redis.Set(ctx, uuidKey, userID, constants.QRLoginUUIDExpire); err != nil {
		return err
	}
	// 记录该用户当前激活uuid（便于下次生成时清理原来的uuid）
	if err := redis.Set(ctx, activeKey, uuid, constants.QRLoginUUIDExpire); err != nil {
		return err
	}
	return nil
}

// GetActiveUUID 获取用户当前激活的UUID（按context维度）
func (s *QRLoginService) GetActiveUUID(ctx context.Context, userID string, ctxName string) (string, error) {
	activeKey := fmt.Sprintf(constants.RedisQRLoginActiveKey, userID+":"+ctxName)
	val, err := redis.Get(ctx, activeKey)
	if err == redis.Nil {
		return "", nil
	}
	return val, err
}

// InvalidateUUID 使指定UUID失效（删除uuid->userID映射）。若传入空串则忽略
func (s *QRLoginService) InvalidateUUID(ctx context.Context, uuid string) error {
	if uuid == "" {
		return nil
	}
	uuidKey := fmt.Sprintf(constants.RedisQRLoginUUIDKey, uuid)
	// 直接删除UUID映射关系
	return redis.Redis().Del(ctx, uuidKey).Err()
}

// ValidateUUID 校验UUID是否有效，刷新TTL保持5分钟有效期
func (s *QRLoginService) ValidateUUID(ctx context.Context, uuid string) (string, bool, error) {
	uuidKey := fmt.Sprintf(constants.RedisQRLoginUUIDKey, uuid)
	userID, err := redis.Get(ctx, uuidKey)
	if err == redis.Nil {
		return "", false, nil
	}
	if err != nil {
		return "", false, err
	}
	// 刷新TTL，保持5分钟有效期
	_ = redis.Redis().Expire(ctx, uuidKey, constants.QRLoginUUIDExpire).Err()
	return userID, true, nil
}

// RefreshActiveTTL 刷新用户当前激活UUID的活跃时间，非关键路径
func (s *QRLoginService) RefreshActiveTTL(ctx context.Context, userID string, ctxName string) {
	activeKey := fmt.Sprintf(constants.RedisQRLoginActiveKey, userID+":"+ctxName)
	_ = redis.Redis().Expire(ctx, activeKey, constants.QRLoginUUIDExpire).Err()
}

// 注意：SaveUUIDMapping, GetUserIDByUUID, InvalidateUUIDMapping 方法已移除
// 现在统一使用 ValidateUUID 方法进行UUID验证和用户ID获取

// SaveBindRecord 保存玩家绑定信息，JSON 字符串
func (s *QRLoginService) SaveBindRecord(ctx context.Context, uuid string, json string) error {
	bindKey := fmt.Sprintf(constants.RedisQRLoginBindKey, uuid)
	return redis.Set(ctx, bindKey, json, constants.QRLoginUUIDExpire)
}

// GetBindRecord 获取玩家绑定信息
func (s *QRLoginService) GetBindRecord(ctx context.Context, uuid string) (string, error) {
	bindKey := fmt.Sprintf(constants.RedisQRLoginBindKey, uuid)
	val, err := redis.Get(ctx, bindKey)
	if err == redis.Nil {
		return "", nil
	}
	return val, err
}

// InvalidateBindRecord 使绑定记录失效
func (s *QRLoginService) InvalidateBindRecord(ctx context.Context, uuid string) error {
	if uuid == "" {
		return nil
	}
	bindKey := fmt.Sprintf(constants.RedisQRLoginBindKey, uuid)
	return redis.Redis().Del(ctx, bindKey).Err()
}

// GetAndDeleteBindRecord 原子地获取并删除玩家绑定信息，避免并发重复发放
func (s *QRLoginService) GetAndDeleteBindRecord(ctx context.Context, uuid string) (string, error) {
	bindKey := fmt.Sprintf(constants.RedisQRLoginBindKey, uuid)

	// 优先尝试使用 Redis GETDEL（Redis >= 6.2）
	val, err := s.tryGetDel(ctx, bindKey)
	if err == nil || err == redis.Nil {
		return val, nil
	}

	// 如果GETDEL不支持，回退到Lua脚本
	if s.isUnsupportedCommandError(err) {
		return s.getDelWithLuaScript(ctx, bindKey)
	}

	return "", err
}

// tryGetDel 尝试使用Redis GETDEL命令
func (s *QRLoginService) tryGetDel(ctx context.Context, key string) (string, error) {
	return redis.Redis().GetDel(ctx, key).Result()
}

// isUnsupportedCommandError 检查是否为不支持的命令错误
func (s *QRLoginService) isUnsupportedCommandError(err error) bool {
	errMsg := strings.ToLower(err.Error())
	return strings.Contains(errMsg, "unknown command") || strings.Contains(errMsg, "unknown")
}

// getDelWithLuaScript 使用Lua脚本实现原子的GET+DEL操作
func (s *QRLoginService) getDelWithLuaScript(ctx context.Context, key string) (string, error) {
	script := "local v = redis.call('GET', KEYS[1]); if v then redis.call('DEL', KEYS[1]); end; return v;"
	res, err := redis.Redis().Eval(ctx, script, []string{key}).Result()

	if err == redis.Nil || res == nil {
		return "", nil
	}
	if err != nil {
		return "", err
	}

	return s.convertToString(res), nil
}

// convertToString 将Redis返回值转换为字符串
func (s *QRLoginService) convertToString(res interface{}) string {
	switch v := res.(type) {
	case string:
		return v
	case []byte:
		return string(v)
	default:
		return fmt.Sprintf("%v", v)
	}
}
