package service

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/go-resty/resty/v2"
)

// 引力引擎服务

var (
	_gravityEngineOnce    sync.Once
	_gravityEngineService *GravityEngineService
)

type GravityEngineService struct {
	client *resty.Client
}

const (
	refreshTokenURL = "/event_center/api/v1/base/wx/access_token/refresh/"
	eventURL        = "/event_center/api/v1/event/collect/"
)

func SingletonGravityEngineService() *GravityEngineService {
	_gravityEngineOnce.Do(func() {
		_gravityEngineService = &GravityEngineService{
			client: resty.New(),
		}
	})
	return _gravityEngineService
}

// RefreshToken 刷新token
func (s *GravityEngineService) RefreshToken(ctx context.Context, appID, accessToken, wxAccessToken string) error {
	if appID == "" || accessToken == "" || wxAccessToken == "" {
		return fmt.Errorf("RefreshToken appID or accessToken or wxAccessToken is empty")
	}

	logger.Logger.InfofCtx(ctx, "RefreshToken start, appID: %s, accessToken: %s, wxAccessToken: %s", appID, accessToken, wxAccessToken)

	now := time.Now().Unix()
	signStr := fmt.Sprintf("app_key=%s&wx_access_token=%s&current_time=%d", appID, wxAccessToken, now)
	signMd5 := strings.ToLower(util.EncodeMD5(signStr))

	resp, err := s.client.
		SetTimeout(10*time.Second).
		R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetQueryParam("access_token", accessToken).
		SetBody(map[string]interface{}{
			"wx_access_token": wxAccessToken,
			"current_time":    now,
			"sign":            signMd5,
		}).
		Post(config.GlobConfig.GravityEngine.BaseURL + refreshTokenURL)
	if err != nil {
		return err
	}
	if resp.StatusCode() != http.StatusOK {
		return fmt.Errorf("RefreshToken refresh token failed, status code: %d", resp.StatusCode())
	}
	var result bean.EventRes
	if err := json.Unmarshal(resp.Body(), &result); err != nil {
		return err
	}
	if result.Code != 0 {
		logger.Logger.WarnfCtx(ctx, "RefreshToken refresh token failed, code: %d, msg: %s, appID: %s, accessToken: %s, wxAccessToken: %s",
			result.Code, result.Msg, appID, accessToken, wxAccessToken)
		return nil
	}
	return nil
}

// ReportPayEvent is reporting pay events
func (s *GravityEngineService) ReportPayEvent(ctx context.Context, accessToken, clientID string, properties map[string]interface{}) error {
	logger.Logger.DebugfCtx(ctx, "ReportPayEvent start, accessToken: %s, clientID: %s, properties: %+v")
	body := make(map[string]interface{})
	body["client_id"] = clientID
	body["event_list"] = make([]map[string]interface{}, 0)

	eventInfo := make(map[string]interface{})
	eventInfo["type"] = "track"
	eventInfo["event"] = "$PayEvent"
	eventInfo["time"] = time.Now().UnixMilli()
	eventInfo["properties"] = properties
	body["event_list"] = append(body["event_list"].([]map[string]interface{}), eventInfo) // 强制转换，请注意body["event_list"]的类型

	// 使用debug打印参数
	logger.Logger.DebugfCtx(ctx, "ReportPayEvent accessToken: %s, clientID: %s", accessToken, clientID)
	logger.Logger.DebugfCtx(ctx, "ReportPayEvent request body: %+v", body)

	resp, err := s.client.
		SetTimeout(10*time.Second).
		R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetHeader("Turbo-Debug-Mode", config.GlobConfig.GravityEngine.Env). // 线上环境: SetHeader("Turbo-Debug-Mode", "0")
		SetQueryParam("access_token", accessToken).
		SetBody(body).
		Post(config.GlobConfig.GravityEngine.BaseURL + eventURL)
	if err != nil {
		return err
	}
	if resp.StatusCode() != http.StatusOK {
		// logger.Logger.Errorf("GravityEngineService ReportPayEvent https code status: %d", resp.StatusCode())
		return fmt.Errorf("GravityEngineService ReportPayEvent https code status: %d", resp.StatusCode())
	}
	eventRes := &bean.EventRes{}
	if err := json.Unmarshal(resp.Body(), &eventRes); err != nil {
		// logger.Logger.Errorf("GravityEngineService ReportPayEvent json unmarshal error: %s", err.Error())
		return err
	}
	if eventRes.Code != 0 {
		// logger.Logger.Errorf("GravityEngineService ReportPayEvent call success but returns code: %d, msg: %s",
		//	eventRes.Code, eventRes.Msg)
		return fmt.Errorf("GravityEngineService ReportPayEvent call success but returns code: %d, msg: %s",
			eventRes.Code, eventRes.Msg)
	}
	return nil
}
