package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/https"
	"git.panlonggame.com/bkxplatform/admin-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/internal/utils"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"
)

var (
	_userOnce    sync.Once
	_userService *UserService
)

// userMappingCacheKey 生成缓存 key（仅 Redis）
func userMappingCacheKey(unionID, newGameID string) string {
	return fmt.Sprintf("user_mapping_status:%s:%s", unionID, newGameID)
}

func marshalStatus(st *UserMappingStatus) (string, error) {
	b, err := json.Marshal(st)
	if err != nil {
		return "", err
	}
	return string(b), nil
}
func unmarshalStatus(s string) (*UserMappingStatus, error) {
	if s == "" {
		return nil, errors.New("empty status json")
	}
	var st UserMappingStatus
	if err := json.Unmarshal([]byte(s), &st); err != nil {
		return nil, err
	}
	return &st, nil
}

type UserService struct{}

func SingletonUserService() *UserService {
	_userOnce.Do(func() {
		_userService = &UserService{}
	})
	return _userService
}

func (s *UserService) Heartbeat(ctx context.Context, action string) error {
	if action != "healthCheck" {
		return errors.New("action can not be empty")
	}
	// 检测数据库状态
	db, err := store.GOrmDB(ctx).DB()
	if err != nil {
		return err
	}
	if err = db.Ping(); err != nil {
		return err
	}
	// 检测 Redis 状态
	_, err = redis.Redis().Ping(ctx).Result()
	if err != nil {
		return err
	}
	return nil
}

func (s *UserService) GetTimestamp(ctx context.Context) (*bean.TimeRes, error) {
	return &bean.TimeRes{
		DateTime:  time.Now().Format(time.RFC3339),
		Timestamp: time.Now().UnixMilli(),
	}, nil
}

// GetUserInfoByOpenID 获取用户信息
// Deprecated: 使用 GetUserInfoByOpenIDOptimized 替代
func (s *UserService) GetUserInfoByOpenID(ctx context.Context,
	code, gameID, channel, adFrom, openID, unionID, sessionKey string,
) (*bean.User, error) {
	// 分布式锁
	openIDLockKey := fmt.Sprintf("%s:%s", constants.SystemLoginLockKey, code)
	isLock := redis.Lock(ctx, openIDLockKey, constants.SystemLoginLockExpire*time.Second)
	if !isLock {
		logger.Logger.Warnf("GetUserInfoByOpenID server is busy, game_id :%s, openID :%s", gameID, openID)
		return nil, constants.ErrSystemServiceIsBusy
	}
	defer redis.UnLock(ctx, openIDLockKey)

	userInfoRes := &bean.UserInfo{
		IsExistUserInfo: constants.FalseStr, // false
	}
	channelInfoRes := &bean.ChannelInfo{}
	userModel := &model.AUser{}
	userMinigameModel := &model.AUserMinigame{}
	user := store.QueryDB().AUser
	userShareCode := store.QueryDB().AUserShareCode

	// 先查找用户是否存在
	userMiniGame := store.QueryDB().AUserMinigame
	userMinigameInfo, err := userMiniGame.WithContext(ctx).
		Where(userMiniGame.IsDeleted.Zero()).
		Where(userMiniGame.OpenID.Eq(openID)).First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		// 新用户注册
		userID := util.UUID()
		userModel.GameID = gameID // 绑定用户所属游戏
		userModel.UserID = userID
		userModel.Channel = channel // 注册
		userModel.AdFrom = adFrom
		err = user.Create(userModel)
		if err != nil {
			return nil, err
		}
		userMinigameModel.UserID = userID
		userMinigameModel.OpenID = openID
		userMinigameModel.UnionID = unionID
		userMinigameModel.SessionKey = sessionKey
		err = userMiniGame.Create(userMinigameModel)
		if err != nil {
			return nil, err
		}

		// 新用户直接创建分享码
		newShareCode := util.UUID()
		userShareCodeModel := &model.AUserShareCode{
			UserID:    userID,
			ShareCode: newShareCode,
			Status:    1, // 1-有效
		}
		err = userShareCode.Create(userShareCodeModel)
		if err != nil {
			logger.Logger.Errorf("Create user share code failed: %v", err)
			// 不影响主流程
		} else {
			channelInfoRes.ShareCode = newShareCode
		}

		userInfoRes.UserID = userModel.UserID
		channelInfoRes.Channel = userModel.Channel
		channelInfoRes.ADFrom = userModel.AdFrom
		channelInfoRes.OpenID = openID
		userInfoRes.RegisterAt = time.Now().UnixMilli()

		// 缓存一致性保障：用户注册成功后立即更新缓存状态
		// 异步更新缓存，将新注册用户标记为老用户（false）
		s.UpdateUserExistsCache(ctx, openID, gameID, false)
		logger.Logger.InfofCtx(ctx, "[GetUserInfoByOpenID] 新用户注册成功，已更新缓存状态, userID: %s, gameID: %s, openID: %s", userID, gameID, openID)

		return &bean.User{
			UserInfo:    userInfoRes,
			ChannelInfo: channelInfoRes,
			IsRegister:  true,
		}, nil
	} else if err != nil {
		return nil, err
	}

	// 将最新session_key更新到数据库
	_, err = userMiniGame.WithContext(ctx).
		Where(userMiniGame.UserID.Eq(userMinigameInfo.UserID)).
		Where(userMiniGame.IsDeleted.Zero()).
		UpdateSimple(userMiniGame.SessionKey.Value(sessionKey), userMiniGame.UnionID.Value(unionID))
	if err != nil {
		return nil, err
	}

	userInfo, err := user.WithContext(ctx).
		Where(user.IsDeleted.Zero()).
		Where(user.UserID.Eq(userMinigameInfo.UserID)).First()
	if err != nil {
		return nil, err
	}
	channelInfoRes.Channel = userInfo.Channel
	channelInfoRes.ADFrom = userInfo.AdFrom
	channelInfoRes.OpenID = userMinigameInfo.OpenID

	// 获取用户分享码
	userShareCodeInfo, err := userShareCode.WithContext(ctx).
		Where(userShareCode.UserID.Eq(userMinigameInfo.UserID), userShareCode.Status.Eq(1)).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Logger.Errorf("Query user share code failed: %v", err)
		// 不影响主流程
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		// 如果用户没有分享码，创建一个
		newShareCode := util.UUID()
		userShareCodeModel := &model.AUserShareCode{
			UserID:    userMinigameInfo.UserID,
			ShareCode: newShareCode,
			Status:    1, // 1-有效
		}
		err = userShareCode.Create(userShareCodeModel)
		if err != nil {
			logger.Logger.Errorf("Create user share code failed: %v", err)
			// 不影响主流程
		} else {
			channelInfoRes.ShareCode = newShareCode
		}
	} else {
		channelInfoRes.ShareCode = userShareCodeInfo.ShareCode
	}

	err = copier.Copy(&userInfoRes, userMinigameInfo)
	if err != nil {
		return nil, err
	}
	userInfoRes.IsExistUserInfo = constants.FalseStr
	userInfoRes.Gender = strconv.Itoa(int(userMinigameInfo.Gender))
	userInfoRes.WatermarkTimestamp = strconv.FormatInt(userMinigameInfo.WatermarkTimestamp, 10)

	// 以NickName作为标识来判断有无小游戏信息
	if userMinigameInfo.NickName != "" {
		userInfoRes.IsExistUserInfo = constants.TrueStr
	}

	userInfoRes.RegisterAt = userInfo.CreatedAt
	return &bean.User{
		UserInfo:    userInfoRes,
		ChannelInfo: channelInfoRes,
		IsRegister:  false,
	}, nil
}

// UserWithRelations 用于一次性查询用户相关信息的结构体
type UserWithRelations struct {
	// 用户小游戏信息
	UserMinigame *model.AUserMinigame
	// 用户基础信息
	User *model.AUser
	// 用户分享码
	ShareCode string
}

// GetUserInfoByOpenIDForTest 测试接口专用的用户信息获取方法，使用双重检查锁 + 细粒度锁优化
func (s *UserService) GetUserInfoByOpenIDForTest(ctx context.Context,
	gameID, channel, adFrom, openID, unionID, sessionKey string,
) (*bean.User, error) {
	methodStart := time.Now()

	userInfoRes := &bean.UserInfo{
		IsExistUserInfo: constants.FalseStr, // false
	}
	channelInfoRes := &bean.ChannelInfo{}

	// 第一次检查：先无锁查询用户是否存在，同时尝试获取所有相关数据
	queryStart := time.Now()
	userWithRelations, err := s.getUserWithRelationsByOpenID(ctx, openID)
	queryDuration := float64(time.Since(queryStart).Nanoseconds()) / 1e6
	logger.Logger.InfofCtx(ctx, "[GetUserInfoByOpenIDForTest] 联合查询耗时: %.2fms, openID: %s", queryDuration, openID)

	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		// 用户不存在，需要创建 - 使用分布式锁保护创建过程
		openIDLockKey := fmt.Sprintf("%s:test:%s:%s", constants.SystemLoginLockKey, gameID, openID)
		isLock := redis.Lock(ctx, openIDLockKey, constants.SystemLoginLockExpire*time.Second)
		if !isLock {
			logger.Logger.WarnfCtx(ctx, "[GetUserInfoByOpenIDForTest] 获取用户创建锁失败, gameID: %s, openID: %s", gameID, openID)
			return nil, constants.ErrSystemServiceIsBusy
		}
		defer redis.UnLock(ctx, openIDLockKey)

		// 第二次检查：在锁内再次确认用户是否存在（双重检查）
		userWithRelations, err = s.getUserWithRelationsByOpenID(ctx, openID)

		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
			// 确认用户不存在，创建新用户
			return s.createNewTestUser(ctx, gameID, channel, adFrom, openID, unionID, sessionKey, methodStart)
		} else if err != nil {
			return nil, err
		}
		// 如果在锁内发现用户已存在，继续执行后续逻辑（释放锁后处理）
	} else if err != nil {
		return nil, err
	}

	// 用户已存在，处理session_key更新（优化：只在真正需要时更新）
	if s.shouldUpdateSessionKey(userWithRelations.UserMinigame, sessionKey, unionID) {
		updateStart := time.Now()
		s.updateSessionKeyOptimized(ctx, userWithRelations.UserMinigame.UserID, sessionKey, unionID)
		updateDuration := float64(time.Since(updateStart).Nanoseconds()) / 1e6
		logger.Logger.InfofCtx(ctx, "[GetUserInfoByOpenIDForTest] session更新耗时: %.2fms, userID: %s", updateDuration, userWithRelations.UserMinigame.UserID)
	}

	// 处理分享码（如果没有则创建）
	if userWithRelations.ShareCode == "" {
		shareCode := s.ensureUserShareCode(ctx, userWithRelations.UserMinigame.UserID)
		userWithRelations.ShareCode = shareCode
	}

	// 组装返回数据（手动赋值替代反射拷贝）
	s.assembleUserInfoManually(userInfoRes, userWithRelations.UserMinigame)
	channelInfoRes.Channel = userWithRelations.User.Channel
	channelInfoRes.ADFrom = userWithRelations.User.AdFrom
	channelInfoRes.OpenID = userWithRelations.UserMinigame.OpenID
	channelInfoRes.ShareCode = userWithRelations.ShareCode

	userInfoRes.RegisterAt = userWithRelations.User.CreatedAt

	// 记录方法总耗时
	totalMethodCostMs := float64(time.Since(methodStart).Nanoseconds()) / 1e6
	logger.Logger.InfofCtx(ctx, "[GetUserInfoByOpenIDForTest] 获取已存在测试用户完成, userID: %s, gameID: %s, 总耗时: %.2fms", userWithRelations.UserMinigame.UserID, gameID, totalMethodCostMs)
	return &bean.User{
		UserInfo:    userInfoRes,
		ChannelInfo: channelInfoRes,
		IsRegister:  false,
	}, nil
}

// GetUserInfoByOpenIDOptimized 获取用户信息的生产环境优化版本
// 基于 GetUserInfoByOpenIDForTest 的优化逻辑，移除测试相关特殊处理
func (s *UserService) GetUserInfoByOpenIDOptimized(ctx context.Context,
	code, gameID, channel, adFrom, openID, unionID, sessionKey string,
) (*bean.User, error) {
	userInfoRes := &bean.UserInfo{
		IsExistUserInfo: constants.FalseStr, // false
	}
	channelInfoRes := &bean.ChannelInfo{}

	// 第一次检查：先无锁查询用户是否存在，同时尝试获取所有相关数据
	userWithRelations, err := s.getUserWithRelationsByOpenID(ctx, openID)

	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		// 用户不存在，需要创建 - 使用分布式锁保护创建过程
		openIDLockKey := fmt.Sprintf("%s:%s:%s", constants.SystemLoginLockKey, gameID, openID)
		isLock := redis.Lock(ctx, openIDLockKey, constants.SystemLoginLockExpire*time.Second)
		if !isLock {
			logger.Logger.WarnfCtx(ctx, "[GetUserInfoByOpenIDOptimized] 获取用户创建锁失败, gameID: %s, openID: %s", gameID, openID)
			return nil, constants.ErrSystemServiceIsBusy
		}
		defer redis.UnLock(ctx, openIDLockKey)

		// 第二次检查：在锁内再次确认用户是否存在（双重检查）
		userWithRelations, err = s.getUserWithRelationsByOpenID(ctx, openID)

		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
			// 确认用户不存在，创建新用户
			return s.createNewOptimizedUser(ctx, gameID, channel, adFrom, openID, unionID, sessionKey)
		} else if err != nil {
			return nil, err
		}
		// 如果在锁内发现用户已存在，继续执行后续逻辑（释放锁后处理）
	} else if err != nil {
		return nil, err
	}

	// 用户已存在，处理session_key更新（优化：只在真正需要时更新）
	if s.shouldUpdateSessionKey(userWithRelations.UserMinigame, sessionKey, unionID) {
		s.updateSessionKeyOptimizedForProduction(ctx, userWithRelations.UserMinigame.UserID, sessionKey, unionID)
	}

	// 处理分享码（如果没有则创建）
	if userWithRelations.ShareCode == "" {
		shareCode := s.ensureUserShareCode(ctx, userWithRelations.UserMinigame.UserID)
		userWithRelations.ShareCode = shareCode
	}

	// 组装返回数据（手动赋值替代反射拷贝）
	s.assembleUserInfoManually(userInfoRes, userWithRelations.UserMinigame)
	channelInfoRes.Channel = userWithRelations.User.Channel
	channelInfoRes.ADFrom = userWithRelations.User.AdFrom
	channelInfoRes.OpenID = userWithRelations.UserMinigame.OpenID
	channelInfoRes.ShareCode = userWithRelations.ShareCode

	userInfoRes.RegisterAt = userWithRelations.User.CreatedAt

	// 记录方法完成日志
	logger.Logger.InfofCtx(ctx, "[GetUserInfoByOpenIDOptimized] 获取已存在用户完成, userID: %s, gameID: %s", userWithRelations.UserMinigame.UserID, gameID)
	return &bean.User{
		UserInfo:    userInfoRes,
		ChannelInfo: channelInfoRes,
		IsRegister:  false,
	}, nil
}

// getUserWithRelationsByOpenID 一次性查询用户及其相关信息
func (s *UserService) getUserWithRelationsByOpenID(ctx context.Context, openID string) (*UserWithRelations, error) {

	start := time.Now()

	// 使用原生SQL进行联合查询，一次性获取所有需要的数据
	var result struct {
		// a_user_minigame 表字段
		UserMinigameID        int32  `gorm:"column:um_id"`
		UserID                string `gorm:"column:user_id"`
		OpenID                string `gorm:"column:open_id"`
		UnionID               string `gorm:"column:union_id"`
		NickName              string `gorm:"column:nick_name"`
		Gender                int32  `gorm:"column:gender"`
		City                  string `gorm:"column:city"`
		Province              string `gorm:"column:province"`
		Country               string `gorm:"column:country"`
		AvatarURL             string `gorm:"column:avatar_url"`
		Language              string `gorm:"column:language"`
		WatermarkAppID        string `gorm:"column:watermark_app_id"`
		WatermarkTimestamp    int64  `gorm:"column:watermark_timestamp"`
		SessionKey            string `gorm:"column:session_key"`
		UserMinigameCreatedAt int64  `gorm:"column:um_created_at"`
		UserMinigameUpdatedAt int64  `gorm:"column:um_updated_at"`
		UserMinigameIsDeleted bool   `gorm:"column:um_is_deleted"`

		// a_user 表字段
		UserTableID   int32  `gorm:"column:u_id"`
		GameID        string `gorm:"column:game_id"`
		Channel       string `gorm:"column:channel"`
		AdFrom        string `gorm:"column:ad_from"`
		UserCreatedAt int64  `gorm:"column:u_created_at"`
		UserUpdatedAt int64  `gorm:"column:u_updated_at"`
		UserIsDeleted bool   `gorm:"column:u_is_deleted"`

		// a_user_share_codes 表字段
		ShareCode string `gorm:"column:share_code"`
	}

	db := store.GOrmDB(ctx)
	err := db.Raw(`
		SELECT
			um.id as um_id, um.user_id, um.open_id, um.union_id, um.nick_name, um.gender,
			um.city, um.province, um.country, um.avatar_url, um.language,
			um.watermark_app_id, um.watermark_timestamp, um.session_key,
			um.created_at as um_created_at, um.updated_at as um_updated_at, um.is_deleted as um_is_deleted,
			u.id as u_id, u.game_id, u.channel, u.ad_from,
			u.created_at as u_created_at, u.updated_at as u_updated_at, u.is_deleted as u_is_deleted,
			usc.share_code
		FROM a_user_minigame um
		LEFT JOIN a_user u ON um.user_id = u.user_id AND u.is_deleted = 0
		LEFT JOIN a_user_share_codes usc ON um.user_id = usc.user_id AND usc.status = 1 AND usc.is_deleted = 0
		WHERE um.open_id = ? AND um.is_deleted = 0
	`, openID).Scan(&result).Error

	// 计算查询耗时
	elapsed := time.Since(start)
	latencyMillis := float64(elapsed.Nanoseconds()) / 1e6

	// 记录性能日志
	fields := map[string]interface{}{
		"latency_millis": fmt.Sprintf("%.3f", latencyMillis),
		"operation":      "user_relations_query",
		"open_id":        openID,
		"game_id":        result.GameID,
	}

	if err != nil {
		fields["error"] = err.Error()
		logger.Logger.InfoWithFiledCtx(ctx, fields, "用户关系查询失败")
		return nil, err
	}

	// 检查是否找到用户
	if result.UserID == "" {
		fields["status"] = "not_found"
		logger.Logger.InfoWithFiledCtx(ctx, fields, "用户关系查询耗时")
		return nil, gorm.ErrRecordNotFound
	}

	logger.Logger.InfoWithFiledCtx(ctx, fields, "用户关系查询耗时")

	// 构造返回结果
	userWithRelations := &UserWithRelations{
		UserMinigame: &model.AUserMinigame{
			ID:                 result.UserMinigameID,
			UserID:             result.UserID,
			OpenID:             result.OpenID,
			UnionID:            result.UnionID,
			NickName:           result.NickName,
			Gender:             result.Gender,
			City:               result.City,
			Province:           result.Province,
			Country:            result.Country,
			AvatarURL:          result.AvatarURL,
			Language:           result.Language,
			WatermarkAppID:     result.WatermarkAppID,
			WatermarkTimestamp: result.WatermarkTimestamp,
			SessionKey:         result.SessionKey,
			CreatedAt:          result.UserMinigameCreatedAt,
			UpdatedAt:          result.UserMinigameUpdatedAt,
			IsDeleted:          result.UserMinigameIsDeleted,
		},
		User: &model.AUser{
			ID:        result.UserTableID,
			GameID:    result.GameID,
			UserID:    result.UserID,
			Channel:   result.Channel,
			AdFrom:    result.AdFrom,
			CreatedAt: result.UserCreatedAt,
			UpdatedAt: result.UserUpdatedAt,
			IsDeleted: result.UserIsDeleted,
		},
		ShareCode: result.ShareCode,
	}

	return userWithRelations, nil
}

// createNewTestUser 创建新的测试用户
func (s *UserService) createNewTestUser(ctx context.Context, gameID, channel, adFrom, openID, unionID, sessionKey string, _ time.Time) (*bean.User, error) {
	userCreationStart := time.Now()
	userID := util.UUID()

	user := store.QueryDB().AUser
	userMiniGame := store.QueryDB().AUserMinigame
	userShareCode := store.QueryDB().AUserShareCode

	userModel := &model.AUser{}
	userMinigameModel := &model.AUserMinigame{}

	userModel.GameID = gameID // 绑定用户所属游戏
	userModel.UserID = userID
	userModel.Channel = channel // 注册
	userModel.AdFrom = adFrom

	err := user.Create(userModel)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[GetUserInfoByOpenIDForTest] 创建用户基础信息失败, userID: %s, err: %v", userID, err)
		return nil, err
	}

	userMinigameModel.UserID = userID
	userMinigameModel.OpenID = openID
	userMinigameModel.UnionID = unionID
	userMinigameModel.SessionKey = sessionKey

	err = userMiniGame.Create(userMinigameModel)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[GetUserInfoByOpenIDForTest] 创建用户小游戏信息失败, userID: %s, err: %v", userID, err)
		return nil, err
	}

	// 新用户直接创建分享码
	newShareCode := util.UUID()
	userShareCodeModel := &model.AUserShareCode{
		UserID:    userID,
		ShareCode: newShareCode,
		Status:    1, // 1-有效
	}
	err = userShareCode.Create(userShareCodeModel)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "[GetUserInfoByOpenIDForTest] 创建用户分享码失败, userID: %s, err: %v", userID, err)
		// 不影响主流程
		newShareCode = ""
	}

	userInfoRes := &bean.UserInfo{
		IsExistUserInfo: constants.FalseStr, // false
	}
	channelInfoRes := &bean.ChannelInfo{}

	userInfoRes.UserID = userModel.UserID
	channelInfoRes.Channel = userModel.Channel
	channelInfoRes.ADFrom = userModel.AdFrom
	channelInfoRes.OpenID = openID
	channelInfoRes.ShareCode = newShareCode

	userInfoRes.RegisterAt = time.Now().UnixMilli()

	// 缓存一致性保障：测试用户注册成功后立即更新缓存状态
	// 异步更新缓存，将新注册用户标记为老用户（false）
	s.UpdateUserExistsCache(ctx, openID, gameID, false)

	// 记录用户创建完成日志
	totalUserCreationCostMs := float64(time.Since(userCreationStart).Nanoseconds()) / 1e6
	logger.Logger.InfofCtx(ctx, "[GetUserInfoByOpenIDForTest] 创建新测试用户完成，已更新缓存状态, userID: %s, gameID: %s, 耗时: %.2fms", userID, gameID, totalUserCreationCostMs)
	return &bean.User{
		UserInfo:    userInfoRes,
		ChannelInfo: channelInfoRes,
		IsRegister:  true,
	}, nil
}

// shouldUpdateSessionKey 判断是否需要更新session_key
func (s *UserService) shouldUpdateSessionKey(userMinigame *model.AUserMinigame, newSessionKey, newUnionID string) bool {
	// 只有当session_key或union_id真正发生变化时才更新
	return (newSessionKey != "" && userMinigame.SessionKey != newSessionKey) ||
		(newUnionID != "" && userMinigame.UnionID != newUnionID)
}

// updateSessionKeyOptimized 优化的session_key更新方法
func (s *UserService) updateSessionKeyOptimized(ctx context.Context, userID, sessionKey, unionID string) {
	// 使用细粒度锁保护session_key更新操作
	sessionUpdateLockKey := fmt.Sprintf("%s:session_update:%s", constants.SystemLoginLockKey, userID)
	isSessionLock := redis.Lock(ctx, sessionUpdateLockKey, 5*time.Second) // 较短的锁时间
	if !isSessionLock {
		// session_key更新失败不影响主流程，使用现有的session_key
		logger.Logger.WarnfCtx(ctx, "[GetUserInfoByOpenIDForTest] 获取session更新锁失败, userID: %s", userID)
		return
	}
	defer redis.UnLock(ctx, sessionUpdateLockKey)

	userMiniGame := store.QueryDB().AUserMinigame
	_, err := userMiniGame.WithContext(ctx).
		Where(userMiniGame.UserID.Eq(userID)).
		Where(userMiniGame.IsDeleted.Zero()).
		UpdateSimple(userMiniGame.SessionKey.Value(sessionKey), userMiniGame.UnionID.Value(unionID))

	if err != nil {
		logger.Logger.WarnfCtx(ctx, "[GetUserInfoByOpenIDForTest] session_key更新失败, userID: %s, err: %v", userID, err)
		// 不返回错误，继续主流程
	}
}

// ensureUserShareCode 确保用户有分享码，如果没有则创建
func (s *UserService) ensureUserShareCode(ctx context.Context, userID string) string {
	userShareCode := store.QueryDB().AUserShareCode

	// 先尝试查询是否已有分享码
	userShareCodeInfo, err := userShareCode.WithContext(ctx).
		Where(userShareCode.UserID.Eq(userID), userShareCode.Status.Eq(1)).First()
	if err == nil {
		return userShareCodeInfo.ShareCode
	}

	// 如果没有分享码，创建一个
	newShareCode := util.UUID()
	userShareCodeModel := &model.AUserShareCode{
		UserID:    userID,
		ShareCode: newShareCode,
		Status:    1, // 1-有效
	}
	err = userShareCode.Create(userShareCodeModel)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "[GetUserInfoByOpenIDForTest] 创建用户分享码失败, userID: %s, err: %v", userID, err)
		return ""
	}
	return newShareCode
}

// assembleUserInfoManually 手动组装用户信息，替代反射拷贝
func (s *UserService) assembleUserInfoManually(userInfoRes *bean.UserInfo, userMinigame *model.AUserMinigame) {
	userInfoRes.UserID = userMinigame.UserID
	userInfoRes.NickName = userMinigame.NickName
	userInfoRes.Gender = strconv.Itoa(int(userMinigame.Gender))
	userInfoRes.City = userMinigame.City
	userInfoRes.Province = userMinigame.Province
	userInfoRes.Country = userMinigame.Country
	userInfoRes.AvatarURL = userMinigame.AvatarURL
	userInfoRes.Language = userMinigame.Language
	userInfoRes.WatermarkAppID = userMinigame.WatermarkAppID
	userInfoRes.WatermarkTimestamp = strconv.FormatInt(userMinigame.WatermarkTimestamp, 10)

	// 设置默认值
	userInfoRes.IsExistUserInfo = constants.FalseStr

	// 以NickName作为标识来判断有无小游戏信息
	if userMinigame.NickName != "" {
		userInfoRes.IsExistUserInfo = constants.TrueStr
	}
}

// createNewOptimizedUser 创建新的优化用户（生产环境版本）
func (s *UserService) createNewOptimizedUser(ctx context.Context, gameID, channel, adFrom, openID, unionID, sessionKey string) (*bean.User, error) {
	userID := util.UUID()

	user := store.QueryDB().AUser
	userMiniGame := store.QueryDB().AUserMinigame
	userShareCode := store.QueryDB().AUserShareCode

	userModel := &model.AUser{}
	userMinigameModel := &model.AUserMinigame{}

	userModel.GameID = gameID // 绑定用户所属游戏
	userModel.UserID = userID
	userModel.Channel = channel // 注册
	userModel.AdFrom = adFrom

	err := user.Create(userModel)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[GetUserInfoByOpenIDOptimized] 创建用户基础信息失败, userID: %s, err: %v", userID, err)
		return nil, err
	}

	userMinigameModel.UserID = userID
	userMinigameModel.OpenID = openID
	userMinigameModel.UnionID = unionID
	userMinigameModel.SessionKey = sessionKey

	err = userMiniGame.Create(userMinigameModel)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[GetUserInfoByOpenIDOptimized] 创建用户小游戏信息失败, userID: %s, err: %v", userID, err)
		return nil, err
	}

	// 新用户直接创建分享码
	newShareCode := util.UUID()
	userShareCodeModel := &model.AUserShareCode{
		UserID:    userID,
		ShareCode: newShareCode,
		Status:    1, // 1-有效
	}
	err = userShareCode.Create(userShareCodeModel)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "[GetUserInfoByOpenIDOptimized] 创建用户分享码失败, userID: %s, err: %v", userID, err)
		// 不影响主流程
		newShareCode = ""
	}

	userInfoRes := &bean.UserInfo{
		IsExistUserInfo: constants.FalseStr, // false
	}
	channelInfoRes := &bean.ChannelInfo{}

	userInfoRes.UserID = userModel.UserID
	channelInfoRes.Channel = userModel.Channel
	channelInfoRes.ADFrom = userModel.AdFrom
	channelInfoRes.OpenID = openID
	channelInfoRes.ShareCode = newShareCode

	userInfoRes.RegisterAt = time.Now().UnixMilli()

	// 缓存一致性保障：用户注册成功后立即更新缓存状态
	// 异步更新缓存，将新注册用户标记为老用户（false）
	s.UpdateUserExistsCache(ctx, openID, gameID, false)

	// 记录用户创建完成日志
	logger.Logger.InfofCtx(ctx, "[GetUserInfoByOpenIDOptimized] 创建新用户完成，已更新缓存状态, userID: %s, gameID: %s", userID, gameID)
	return &bean.User{
		UserInfo:    userInfoRes,
		ChannelInfo: channelInfoRes,
		IsRegister:  true,
	}, nil
}

// updateSessionKeyOptimizedForProduction 生产环境优化的session_key更新方法
func (s *UserService) updateSessionKeyOptimizedForProduction(ctx context.Context, userID, sessionKey, unionID string) {
	// 使用细粒度锁保护session_key更新操作
	sessionUpdateLockKey := fmt.Sprintf("%s:session_update:%s", constants.SystemLoginLockKey, userID)
	isSessionLock := redis.Lock(ctx, sessionUpdateLockKey, 5*time.Second) // 较短的锁时间
	if !isSessionLock {
		// session_key更新失败不影响主流程，使用现有的session_key
		logger.Logger.WarnfCtx(ctx, "[GetUserInfoByOpenIDOptimized] 获取session更新锁失败, userID: %s", userID)
		return
	}
	defer redis.UnLock(ctx, sessionUpdateLockKey)

	userMiniGame := store.QueryDB().AUserMinigame
	_, err := userMiniGame.WithContext(ctx).
		Where(userMiniGame.UserID.Eq(userID)).
		Where(userMiniGame.IsDeleted.Zero()).
		UpdateSimple(userMiniGame.SessionKey.Value(sessionKey), userMiniGame.UnionID.Value(unionID))

	if err != nil {
		logger.Logger.WarnfCtx(ctx, "[GetUserInfoByOpenIDOptimized] session_key更新失败, userID: %s, err: %v", userID, err)
		// 不返回错误，继续主流程
	}
}

// GetSubscribeUserInfoByOpenID returns the user
func (s *UserService) GetSubscribeUserInfoByOpenID(ctx context.Context, openID, unionID string) (*bean.SubscribeUserInfo, error) {
	// 使用Redis分布式锁
	openIDLockKey := fmt.Sprintf("%s:%s", constants.SystemLoginSubscribeLockKey, openID)
	isLock := redis.Lock(ctx, openIDLockKey, constants.SystemLoginSubscribeLockExpire*time.Second)
	if !isLock {
		logger.Logger.Warnf("GetSubscribeUserInfoByOpenID server is busy, openID :%s", openID)
		return nil, constants.ErrSystemServiceIsBusy
	}
	defer redis.UnLock(ctx, openIDLockKey)

	userInfoRes := &bean.SubscribeUserInfo{}
	userSubscribeModel := &model.AUserSubscribe{}

	userSubscribe := store.QueryDB().AUserSubscribe
	userMinigameInfo, err := userSubscribe.WithContext(ctx).Where(userSubscribe.OpenID.Eq(openID)).First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		userID := util.UUID()
		userSubscribeModel.UserID = userID
		userSubscribeModel.OpenID = openID
		userSubscribeModel.UnionID = unionID
		err = userSubscribe.Create(userSubscribeModel)
		if err != nil {
			return nil, err
		}
		userInfoRes.UserID = userID
		userInfoRes.OpenID = openID
		userInfoRes.HasUnionID = unionID != ""
		return userInfoRes, nil
	} else if err != nil {
		return nil, err
	}

	// 只在现有记录没有unionID且提供了新的有效unionID时才更新
	if userMinigameInfo.UnionID == "" && unionID != "" {
		_, err = userSubscribe.WithContext(ctx).Where(userSubscribe.UserID.Eq(userMinigameInfo.UserID)).
			UpdateSimple(userSubscribe.UnionID.Value(unionID))
		if err != nil {
			return nil, err
		}
	}

	userInfoRes.UserID = userMinigameInfo.UserID
	userInfoRes.OpenID = userMinigameInfo.OpenID
	userInfoRes.HasUnionID = unionID != "" || userMinigameInfo.UnionID != ""
	return userInfoRes, nil
}

// GetAllGameList 获取所有游戏列表
func (s *UserService) GetAllGameList(ctx context.Context) ([]*model.MGame, error) {
	game := store.QueryDB().MGame
	gameList, err := game.WithContext(ctx).Where(game.IsDeleted.Zero()).Find()
	if err != nil {
		return nil, err
	}
	return gameList, nil
}

// GetGameInfo 获取游戏信息
func (s *UserService) GetGameInfo(ctx context.Context, gameID string) (*model.MGame, error) {
	game := store.QueryDB().MGame
	gameInfo, err := game.WithContext(ctx).Where(game.GameID.Eq(gameID)).Where(game.IsDeleted.Zero()).First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Logger.Errorf("UserService GetGameInfo: 未找到游戏的信息, gameID :%s", gameID)
		return nil, errors.New("未找到游戏的信息")
	} else if err != nil {
		// 区分context相关错误和真正的系统错误
		if errors.Is(err, context.Canceled) {
			logger.Logger.InfofCtx(ctx, "[GetGameInfo] 查询游戏信息被客户端取消, gameID: %s", gameID)
		} else if errors.Is(err, context.DeadlineExceeded) {
			logger.Logger.WarnfCtx(ctx, "[GetGameInfo] 查询游戏信息超时, gameID: %s", gameID)
		} else {
			logger.Logger.ErrorfCtx(ctx, "[GetGameInfo] 查询游戏信息失败, gameID: %s, err: %v", gameID, err)
		}
		return nil, err
	}

	logger.Logger.InfofCtx(ctx, "[GetGameInfo] 查询游戏信息成功, gameID: %s, gameName: %s", gameID, gameInfo.Name)
	return gameInfo, nil
}

// GetBindGravityAppID 获取绑定过引力APPID
func (s *UserService) GetBindGravityAppID(ctx context.Context, gameID string) (string, string, error) {
	game := store.QueryDB().MGame
	gameInfo, err := game.WithContext(ctx).Where(game.GameID.Eq(gameID)).Where(game.IsDeleted.Zero()).First()
	if err != nil { // 无需判断是否存在，此游戏不存在，则系统异常
		return "", "", err
	}
	if gameInfo.GravityAppID == "" {
		return "", "", nil
	}
	return gameInfo.GravityAccessToken, gameInfo.GravityAppID, nil
}

// GetMinigameUserInfo gets the
func (s *UserService) GetMinigameUserInfo(ctx context.Context, userID string) (*bean.UserInfo, error) {
	userMiniGame := store.QueryDB().AUserMinigame
	userMinigameInfo, err := userMiniGame.WithContext(ctx).Where(userMiniGame.UserID.Eq(userID)).First() // 缺少is_deleted条件, 但线上不会主动删除用户，所以不会影响
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Logger.Errorf(constants.LogUserIDNotFound, userID)
		return nil, nil
	} else if err != nil {
		// 区分context相关错误和真正的系统错误
		if errors.Is(err, context.Canceled) {
			logger.Logger.InfofCtx(ctx, "[GetMinigameUserInfo] 查询微信小游戏用户信息被客户端取消, userID: %s", userID)
		} else if errors.Is(err, context.DeadlineExceeded) {
			logger.Logger.WarnfCtx(ctx, "[GetMinigameUserInfo] 查询微信小游戏用户信息超时, userID: %s", userID)
		} else {
			logger.Logger.ErrorfCtx(ctx, "[GetMinigameUserInfo] 查询微信小游戏用户信息失败, userID: %s, err: %v", userID, err)
		}
		return nil, err
	}

	logger.Logger.InfofCtx(ctx, "[GetMinigameUserInfo] 查询微信小游戏用户信息成功, userID: %s, openID: %s", userID, userMinigameInfo.OpenID)

	userInfo := &bean.UserInfo{}
	err = copier.Copy(&userInfo, userMinigameInfo)
	if err != nil {
		return nil, err
	}
	userInfo.Gender = strconv.Itoa(int(userMinigameInfo.Gender))
	userInfo.WatermarkTimestamp = strconv.FormatInt(userMinigameInfo.WatermarkTimestamp, 10)
	if userMinigameInfo.NickName != "" {
		userInfo.IsExistUserInfo = constants.TrueStr // 用于加密，所以使用字符串类型
	}
	return userInfo, nil
}

// GetDouyinUserInfo gets the
func (s *UserService) GetDouyinUserInfo(ctx context.Context, userID string) (*bean.UserInfo, error) {
	douyin := store.QueryDB().AUserDouyin
	douyinUserInfo, err := douyin.WithContext(ctx).Where(douyin.UserID.Eq(userID)).First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Logger.Errorf(constants.LogUserIDNotFound, userID)
		return nil, nil
	} else if err != nil {
		// 区分context相关错误和真正的系统错误
		if errors.Is(err, context.Canceled) {
			logger.Logger.InfofCtx(ctx, "[GetDouyinUserInfo] 查询抖音小游戏用户信息被客户端取消, userID: %s", userID)
		} else if errors.Is(err, context.DeadlineExceeded) {
			logger.Logger.WarnfCtx(ctx, "[GetDouyinUserInfo] 查询抖音小游戏用户信息超时, userID: %s", userID)
		} else {
			logger.Logger.ErrorfCtx(ctx, "[GetDouyinUserInfo] 查询抖音小游戏用户信息失败, userID: %s, err: %v", userID, err)
		}
		return nil, err
	}

	logger.Logger.InfofCtx(ctx, "[GetDouyinUserInfo] 查询抖音小游戏用户信息成功, userID: %s, openID: %s", userID, douyinUserInfo.OpenID)

	userInfo := &bean.UserInfo{}
	err = copier.Copy(&userInfo, douyinUserInfo)
	if err != nil {
		return nil, err
	}
	userInfo.Gender = strconv.Itoa(int(douyinUserInfo.Gender))
	userInfo.WatermarkTimestamp = strconv.FormatInt(douyinUserInfo.WatermarkTimestamp, 10)
	if douyinUserInfo.NickName != "" {
		userInfo.IsExistUserInfo = constants.TrueStr // 用于加密，所以使用字符串类型
	}
	return userInfo, nil
}

// GetMinigameModel
func (s *UserService) GetMinigameModel(ctx context.Context, userID string) (*model.AUserMinigame, error) {

	start := time.Now()

	userMiniGame := store.QueryDB().AUserMinigame
	userMinigameInfo, err := userMiniGame.WithContext(ctx).Where(userMiniGame.UserID.Eq(userID)).First()

	// 计算查询耗时
	elapsed := time.Since(start)
	latencyMillis := float64(elapsed.Nanoseconds()) / 1e6

	// 记录性能日志
	fields := map[string]interface{}{
		"latency_millis": fmt.Sprintf("%.3f", latencyMillis),
		"operation":      "minigame_model_query",
		"user_id":        userID,
	}

	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		fields["status"] = "not_found"
		logger.Logger.InfoWithFiledCtx(ctx, fields, "小游戏模型查询耗时")
		return nil, nil
	} else if err != nil {
		fields["error"] = err.Error()
		logger.Logger.InfoWithFiledCtx(ctx, fields, "小游戏模型查询失败")
		return nil, err
	}

	fields["open_id"] = userMinigameInfo.OpenID
	logger.Logger.InfoWithFiledCtx(ctx, fields, "小游戏模型查询耗时")

	return userMinigameInfo, nil
}

func (s *UserService) GetUserDouyinModel(ctx context.Context, userID string) (*model.AUserDouyin, error) {
	userDouyin := store.QueryDB().AUserDouyin
	userDouyinInfo, err := userDouyin.WithContext(ctx).Where(userDouyin.UserID.Eq(userID)).First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Logger.Errorf(constants.LogUserIDNotFound, userID)
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return userDouyinInfo, nil
}

func (s *UserService) GetOpenIDMapByUserIDs(ctx context.Context, userIDs []string) (map[string]string, error) {
	var openIDMap map[string]string
	userDouyin := store.QueryDB().AUserDouyin
	users, err := userDouyin.WithContext(ctx).Where(userDouyin.UserID.In(userIDs...), userDouyin.IsDeleted.Zero()).Find()
	if err != nil {
		return nil, err
	}
	for _, user := range users {
		if openIDMap == nil {
			openIDMap = make(map[string]string)
		}
		openIDMap[user.OpenID] = user.UserID
	}
	return openIDMap, nil
}

func (s *UserService) UpdateMinigameInfo(ctx context.Context, userID string, userInfo *bean.WechatUserInfo) (*bean.UpdateMinigameInfoRes, error) {
	userMiniGame := store.QueryDB().AUserMinigame

	_, err := userMiniGame.WithContext(ctx).Where(userMiniGame.UserID.Eq(userID)).UpdateSimple(
		userMiniGame.OpenID.Value(userInfo.OpenID),
		userMiniGame.UnionID.Value(userInfo.UnionID),
		// userMiniGame.SessionKey.Value(userInfo.SessionKey), // 不在此处更新，在login中更新
		userMiniGame.NickName.Value(userInfo.NickName),
		userMiniGame.Gender.Value(userInfo.Gender),
		userMiniGame.City.Value(userInfo.City),
		userMiniGame.Province.Value(userInfo.Province),
		userMiniGame.Country.Value(userInfo.Country),
		userMiniGame.AvatarURL.Value(userInfo.AvatarURL),
		userMiniGame.WatermarkAppID.Value(userInfo.Watermark.AppID),
		userMiniGame.WatermarkTimestamp.Value(userInfo.Watermark.Timestamp),
	)
	if err != nil {
		return nil, err
	}

	// 缓存一致性：更新用户信息后清除相关缓存
	if userInfo.OpenID != "" {
		cacheManager := utils.NewCacheManager()
		if cacheErr := cacheManager.InvalidateUserCache(ctx, userInfo.OpenID); cacheErr != nil {
			logger.Logger.WarnfCtx(ctx, "[UpdateMinigameInfo] 清除用户缓存失败, openID: %s, err: %v", userInfo.OpenID, cacheErr)
			// 缓存清除失败不影响主流程
		}
	}

	return nil, err
}

func (s *UserService) UpdateMinigameInfoV2(ctx context.Context, userID string, userInfo *bean.WechatUserInfo) error {
	userMiniGame := store.QueryDB().AUserMinigame

	if _, err := userMiniGame.WithContext(ctx).Where(userMiniGame.UserID.Eq(userID)).Updates(
		&model.AUserMinigame{
			NickName:           userInfo.NickName,
			Gender:             userInfo.Gender,
			City:               userInfo.City,
			Province:           userInfo.Province,
			Country:            userInfo.Country,
			AvatarURL:          userInfo.AvatarURL,
			WatermarkAppID:     userInfo.Watermark.AppID,
			WatermarkTimestamp: userInfo.Watermark.Timestamp,
		},
	); err != nil {
		return err
	}

	// 缓存一致性：更新用户信息后清除相关缓存
	if userInfo.OpenID != "" {
		cacheManager := utils.NewCacheManager()
		if cacheErr := cacheManager.InvalidateUserCache(ctx, userInfo.OpenID); cacheErr != nil {
			logger.Logger.WarnfCtx(ctx, "[UpdateMinigameInfoV2] 清除用户缓存失败, openID: %s, err: %v", userInfo.OpenID, cacheErr)
			// 缓存清除失败不影响主流程
		}
	}

	return nil
}

// GetOpenIDByUserID 根据userID获取openID，用于缓存管理
func (s *UserService) GetOpenIDByUserID(ctx context.Context, userID string) (string, error) {
	userMiniGame := store.QueryDB().AUserMinigame
	userMinigameInfo, err := userMiniGame.WithContext(ctx).
		Where(userMiniGame.UserID.Eq(userID)).
		Where(userMiniGame.IsDeleted.Zero()).
		First()
	if err != nil {
		return "", err
	}
	return userMinigameInfo.OpenID, nil
}

func (s *UserService) UpdateDouyinInfo(ctx context.Context, userID string, userInfo *bean.DouyinUserInfo) error {
	douyin := store.QueryDB().AUserDouyin

	if _, err := douyin.WithContext(ctx).Where(douyin.UserID.Eq(userID)).Updates(&model.AUserDouyin{
		NickName:           userInfo.NickName,
		AvatarURL:          userInfo.AvatarURL,
		Gender:             userInfo.Gender,
		City:               userInfo.City,
		Province:           userInfo.Province,
		Country:            userInfo.Country,
		WatermarkAppID:     userInfo.Watermark.AppID,
		WatermarkTimestamp: userInfo.Watermark.Timestamp,
		RealNameAuth:       userInfo.RealNameAuthenticationStatus,
	}); err != nil {
		return err
	}

	// 缓存一致性：更新抖音用户信息后清除相关缓存
	// 需要通过userID获取openID来清除缓存
	cacheManager := utils.NewCacheManager()
	if cacheErr := cacheManager.InvalidateUserCacheByUserID(ctx, userID, s.GetOpenIDByUserID); cacheErr != nil {
		logger.Logger.WarnfCtx(ctx, "[UpdateDouyinInfo] 清除用户缓存失败, userID: %s, err: %v", userID, cacheErr)
		// 缓存清除失败不影响主流程
	}

	return nil
}

// GetUserMinigameInfo
func (s *UserService) GetUserMinigameInfo(ctx context.Context, userID string) (*bean.UserMinigameInfo, error) {
	user := store.QueryDB().AUserMinigame
	userInfo, err := user.WithContext(ctx).Where(user.UserID.Eq(userID)).First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Logger.Errorf(constants.LogUserIDNotFound, userID)
		return nil, nil
	} else if err != nil {
		return nil, err
	}

	return &bean.UserMinigameInfo{
		UserID: userInfo.UserID,
	}, nil
}

// GetUserInfo
func (s *UserService) GetUserInfo(ctx context.Context, userID string) (*model.AUser, error) {

	start := time.Now()

	user := store.QueryDB().AUser
	userInfo, err := user.WithContext(ctx).Where(user.UserID.Eq(userID)).First()

	// 计算查询耗时
	elapsed := time.Since(start)
	latencyMillis := float64(elapsed.Nanoseconds()) / 1e6

	// 记录性能日志
	fields := map[string]interface{}{
		"latency_millis": fmt.Sprintf("%.3f", latencyMillis),
		"operation":      "user_info_query",
		"user_id":        userID,
	}

	if err != nil {
		// 区分context相关错误和真正的系统错误
		if errors.Is(err, context.Canceled) {
			logger.Logger.InfofCtx(ctx, "[GetUserInfo] 查询用户信息被客户端取消, userID: %s", userID)
		} else if errors.Is(err, context.DeadlineExceeded) {
			logger.Logger.WarnfCtx(ctx, "[GetUserInfo] 查询用户信息超时, userID: %s", userID)
		} else {
			logger.Logger.WarnfCtx(ctx, "UserService GetUserInfo err: %v", err)
		}
		return nil, err
	}

	fields["game_id"] = userInfo.GameID
	logger.Logger.InfoWithFiledCtx(ctx, fields, "用户信息查询耗时")

	return userInfo, nil
}

// ValidateUserExists 验证game_id和user_id是否存在于a_user表中
func (s *UserService) ValidateUserExists(ctx context.Context, gameID, userID string) error {
	user := store.QueryDB().AUser
	count, err := user.WithContext(ctx).
		Where(user.GameID.Eq(gameID)).
		Where(user.UserID.Eq(userID)).
		Where(user.IsDeleted.Zero()).
		Count()

	if err != nil {
		return err
	}

	if count == 0 {
		return constants.ErrUserNotExists
	}

	return nil
}

// SetBlacklistToken set blacklist token
func (s *UserService) SetBlacklistToken(ctx context.Context, token string) error {
	key := fmt.Sprintf(constants.RedisTokenBlackList, token)
	err := redis.Set(ctx, key, true, constants.TwoWeek)
	if err != nil {
		return err
	}
	return nil
}

// UpdateUserSessionFrom
func (s *UserService) UpdateUserSessionFrom(ctx context.Context, openID string, userSessionFrom *bean.UserSessionFrom) error {
	// 添加redis 分布式锁
	openIDLockKey := fmt.Sprintf("%s:%s", constants.SystemUserSessionFromLockKey, openID)
	isLock := redis.Lock(ctx, openIDLockKey, constants.SystemUserSessionFromLockExpire*time.Second)
	if !isLock {
		logger.Logger.Warnf("UpdateUserSessionFrom server is busy, openID :%s", openID)
		return constants.ErrSystemServiceIsBusy
	}
	defer redis.UnLock(ctx, openIDLockKey)

	player := store.QueryDB().AGamePlayer
	_, err := player.WithContext(ctx).Where(player.OpenID.Eq(openID)).First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		// 创建玩家信息
		playerModel := &model.AGamePlayer{
			OpenID: openID,
		}
		err = player.Create(playerModel)
		if err != nil {
			logger.Logger.ErrorfCtx(ctx, "UpdateUserSessionFrom Not found user Create playerInfo: %v", err)
		}
	} else if err != nil {
		return err
	}
	if userSessionFrom == nil {
		userSessionFrom = &bean.UserSessionFrom{}
	}
	var customData []byte
	if userSessionFrom.CustomData != nil {
		customData, err = json.Marshal(userSessionFrom.CustomData)
		if err != nil {
			return err
		}
	}
	if _, err := player.WithContext(ctx).Where(player.OpenID.Eq(openID)).Updates(&model.AGamePlayer{
		// UserID: userMinigameInfo.UserID,
		// RoleID:              userSessionFrom.RoleID,
		PlayerID:            userSessionFrom.PlayerID,
		PlayerName:          userSessionFrom.PlayerName,
		PlayerLevel:         userSessionFrom.PlayerLevel,
		RechargeTotalAmount: userSessionFrom.RechargeTotalAmount,
		Zone:                userSessionFrom.Zone,
		CustomData:          string(customData),
	}); err != nil {
		return err
	}
	return nil
}

// GetUserSessionFrom
func (s *UserService) GetUserSessionFrom(ctx context.Context, openID string) (*model.AGamePlayer, error) {
	player := store.QueryDB().AGamePlayer
	playerInfo, err := player.WithContext(ctx).Where(player.OpenID.Eq(openID)).First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return playerInfo, nil
}

// GetAccessTokenByOpenID
func (s *UserService) GetAccessTokenByOpenID(ctx context.Context, openID string) (string, error) {

	start := time.Now()

	// 第一步：查询用户小游戏信息
	userMinigame := store.QueryDB().AUserMinigame
	userMinigameInfo, err := userMinigame.WithContext(ctx).Where(userMinigame.OpenID.Eq(openID), userMinigame.IsDeleted.Zero()).First()
	if err != nil {
		elapsed := time.Since(start)
		latencyMillis := float64(elapsed.Nanoseconds()) / 1e6

		fields := map[string]interface{}{
			"latency_millis": fmt.Sprintf("%.3f", latencyMillis),
			"operation":      "access_token_query",
			"open_id":        openID,
			"step":           "user_minigame_query",
		}

		if errors.Is(err, gorm.ErrRecordNotFound) {
			fields["error"] = "openID not found"
			logger.Logger.InfoWithFiledCtx(ctx, fields, "访问令牌查询失败")
			return "", fmt.Errorf("user with openID %s not found", openID)
		}

		fields["error"] = err.Error()
		logger.Logger.InfoWithFiledCtx(ctx, fields, "访问令牌查询失败")
		return "", err
	}

	// 第二步：查询用户基础信息
	user := store.QueryDB().AUser
	userInfo, err := user.WithContext(ctx).Where(user.UserID.Eq(userMinigameInfo.UserID), user.IsDeleted.Zero()).First()
	if err != nil {
		elapsed := time.Since(start)
		latencyMillis := float64(elapsed.Nanoseconds()) / 1e6

		fields := map[string]interface{}{
			"latency_millis": fmt.Sprintf("%.3f", latencyMillis),
			"operation":      "access_token_query",
			"open_id":        openID,
			"user_id":        userMinigameInfo.UserID,
			"step":           "user_info_query",
		}

		if errors.Is(err, gorm.ErrRecordNotFound) {
			fields["error"] = "userID not found"
			logger.Logger.InfoWithFiledCtx(ctx, fields, "访问令牌查询失败")
			return "", fmt.Errorf("user with ID %s not found", userMinigameInfo.UserID)
		}

		fields["error"] = err.Error()
		logger.Logger.InfoWithFiledCtx(ctx, fields, "访问令牌查询失败")
		return "", err
	}

	// 第三步：查询小游戏配置信息
	minigame := store.QueryDB().AConfigMinigame
	configMinigame, err := minigame.WithContext(ctx).Where(minigame.GameID.Eq(userInfo.GameID), minigame.IsDeleted.Zero()).First()

	// 计算总耗时
	elapsed := time.Since(start)
	latencyMillis := float64(elapsed.Nanoseconds()) / 1e6

	// 记录性能日志
	fields := map[string]interface{}{
		"latency_millis": fmt.Sprintf("%.3f", latencyMillis),
		"operation":      "access_token_query",
		"open_id":        openID,
		"user_id":        userMinigameInfo.UserID,
		"game_id":        userInfo.GameID,
	}

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			fields["error"] = "gameID not found"
			logger.Logger.InfoWithFiledCtx(ctx, fields, "访问令牌查询失败")
			return "", fmt.Errorf("game with ID %s not found", userInfo.GameID)
		}

		fields["error"] = err.Error()
		logger.Logger.InfoWithFiledCtx(ctx, fields, "访问令牌查询失败")
		return "", err
	}

	logger.Logger.InfoWithFiledCtx(ctx, fields, "访问令牌查询耗时")
	return configMinigame.AccessToken, nil
}

// GetMinigameModelByOpenID gets minigame model by openID
func (s *UserService) GetMinigameModelByOpenID(ctx context.Context, openID string) (*model.AUserMinigame, error) {
	userMiniGame := store.QueryDB().AUserMinigame
	userMinigameInfo, err := userMiniGame.WithContext(ctx).
		Where(userMiniGame.OpenID.Eq(openID), userMiniGame.IsDeleted.Zero()).First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Logger.Errorf("GetMinigameModelByOpenID openID not found: %s", openID)
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return userMinigameInfo, nil
}

// GetWechatUserInfo gets user information from WeChat API
func (s *UserService) GetWechatUserInfo(ctx context.Context, accessToken, openID string) (*bean.WechatUserInfo, error) {
	wechatHttpService := https.SingletonWechatHttpService()
	return wechatHttpService.GetUserInfo(ctx, accessToken, openID)
}

// GetSubscribeUnionIDByOpenID checks if unionID exists for a given openID in the a_user_subscribe table
func (s *UserService) GetSubscribeUnionIDByOpenID(ctx context.Context, openID string) (string, error) {
	userSubscribe := store.QueryDB().AUserSubscribe
	userSubscribeInfo, err := userSubscribe.WithContext(ctx).
		Where(userSubscribe.OpenID.Eq(openID), userSubscribe.IsDeleted.Zero()).First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return "", nil
	} else if err != nil {
		return "", err
	}
	return userSubscribeInfo.UnionID, nil
}

// CheckUserBan 检查用户是否被封禁
func (s *UserService) CheckUserBan(ctx context.Context, userID, gameID string) (bool, error) {
	now := time.Now().Unix()
	ban := store.QueryDB().MUserBan

	banList, err := ban.WithContext(ctx).
		Where(ban.UserID.Eq(userID)).
		Where(ban.GameID.Eq(gameID)).
		Where(ban.Status.Eq(constants.BanStatusActive)).
		Where(
			ban.Where(ban.BanEndTime.Gt(now)).
				Or(ban.BanEndTime.Eq(constants.BanDurationForever)),
		).
		Find()

	if err != nil {
		return false, err
	}

	return len(banList) > 0, nil
}

// GetSubscribeUnionIDByUserID 通过userID获取订阅用户的unionID
func (s *UserService) GetSubscribeUnionIDByUserID(ctx context.Context, userID string) (*model.AUserSubscribe, error) {
	userSubscribe := store.QueryDB().AUserSubscribe
	userSubscribeInfo, err := userSubscribe.WithContext(ctx).
		Where(userSubscribe.UserID.Eq(userID), userSubscribe.IsDeleted.Zero()).First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Logger.ErrorfCtx(ctx, "GetSubscribeUnionIDByUserID userID not found: %s", userID)
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return userSubscribeInfo, nil
}

// GetShareCodeByUserID 通过user_id查找分享码
func (s *UserService) GetShareCodeByUserID(ctx context.Context, userID string) (string, error) {
	if userID == "" {
		return "", errors.New("user_id cannot be empty")
	}

	start := time.Now()

	userShareCode := store.QueryDB().AUserShareCode
	shareCodeInfo, err := userShareCode.WithContext(ctx).
		Where(userShareCode.UserID.Eq(userID), userShareCode.Status.Eq(1)).
		First()

	// 计算查询耗时
	elapsed := time.Since(start)
	latencyMillis := float64(elapsed.Nanoseconds()) / 1e6

	// 记录性能日志
	fields := map[string]interface{}{
		"latency_millis": fmt.Sprintf("%.3f", latencyMillis),
		"operation":      "share_code_query",
		"user_id":        userID,
	}

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			fields["status"] = "not_found"
			logger.Logger.InfoWithFiledCtx(ctx, fields, "分享码查询耗时")
			return "", nil // 没有找到分享码返回空字符串
		}
		// 区分context相关错误和真正的系统错误
		if errors.Is(err, context.Canceled) {
			logger.Logger.InfofCtx(ctx, "[GetShareCodeByUserID] 查询分享码被客户端取消, userID: %s", userID)
		} else if errors.Is(err, context.DeadlineExceeded) {
			logger.Logger.WarnfCtx(ctx, "[GetShareCodeByUserID] 查询分享码超时, userID: %s", userID)
		} else {
			logger.Logger.ErrorfCtx(ctx, "[GetShareCodeByUserID] 查询分享码失败, userID: %s, err: %v", userID, err)
		}
		return "", err
	}

	fields["share_code"] = shareCodeInfo.ShareCode
	logger.Logger.InfoWithFiledCtx(ctx, fields, "分享码查询耗时")

	return shareCodeInfo.ShareCode, nil
}

// GetUserIDByUnionID 通过UnionID获取UserID
func (s *UserService) GetUserIDByUnionID(ctx context.Context, unionID string, gameID string) (string, error) {
	userMiniGame := store.QueryDB().AUserMinigame
	user := store.QueryDB().AUser

	// 先通过 union_id 查询，结果集很小
	var userIDs []string
	err := userMiniGame.WithContext(ctx).
		Select(userMiniGame.UserID).
		Where(userMiniGame.UnionID.Eq(unionID)).
		Where(userMiniGame.IsDeleted.Zero()).
		Scan(&userIDs)

	if err != nil {
		return "", err
	}

	if len(userIDs) == 0 {
		return "", constants.ErrUserIDNotFound
	}

	// 再验证 game_id
	var userID string
	err = user.WithContext(ctx).
		Select(user.UserID).
		Where(user.UserID.In(userIDs...)).
		Where(user.GameID.Eq(gameID)).
		Where(user.IsDeleted.Zero()).
		Limit(1).
		Pluck(user.UserID, &userID)

	if err != nil {
		return "", err
	}

	if userID == "" {
		return "", constants.ErrUserIDNotFound
	}

	return userID, nil
}

// GetSubscribeMappedUserID 用户ID映射功能 - 临时过渡逻辑
// TODO: 此功能为临时过渡逻辑，后续需要完全移除
// 当game_id为指定的新游戏ID时，通过union_id查找旧游戏ID下是否存在对应的user_id
// 如果存在，返回旧游戏下的user_id；如果不存在，返回空字符串
// Deprecated: 请使用 GetUserNewStatusAndMappedID 以同时获得新用户判定与映射结果，避免重复查询。
func (s *UserService) GetSubscribeMappedUserID(ctx context.Context, unionID string, gameID string) (string, error) {
	// 功能开关检查
	if !config.GlobConfig.UserIDMapping.Enabled {
		logger.Logger.DebugfCtx(ctx, "[用户ID映射] 功能已禁用")
		return "", nil
	}

	// 仅对指定的新游戏ID进行映射
	if gameID != config.GlobConfig.UserIDMapping.NewGameID {
		logger.Logger.DebugfCtx(ctx, "[用户ID映射] 当前游戏ID不需要映射: %s", gameID)
		return "", nil
	}

	// 参数验证
	if unionID == "" {
		logger.Logger.DebugfCtx(ctx, "[用户ID映射] UnionID为空，跳过映射")
		return "", nil
	}

	logger.Logger.InfofCtx(ctx, "[用户ID映射] 开始查找映射用户, UnionID: %s, 新游戏ID: %s, 旧游戏ID: %s",
		unionID, config.GlobConfig.UserIDMapping.NewGameID, config.GlobConfig.UserIDMapping.OldGameID)

	// 通过UnionID在旧游戏ID下查找用户
	mappedUserID, err := s.GetUserIDByUnionIDSafe(ctx, unionID, config.GlobConfig.UserIDMapping.NewGameID)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "[用户ID映射] 查找映射用户失败: %v, UnionID: %s", err, unionID)
		return "", nil // 映射失败不影响正常流程
	}

	if mappedUserID != "" {
		logger.Logger.InfofCtx(ctx, "[用户ID映射] 找到映射用户, 旧UserID: %s, UnionID: %s", mappedUserID, unionID)

		// 记录映射成功的监控日志
		fields := map[string]interface{}{
			"operation":      "user_id_mapping_success",
			"union_id":       unionID,
			"new_game_id":    config.GlobConfig.UserIDMapping.NewGameID,
			"old_game_id":    config.GlobConfig.UserIDMapping.OldGameID,
			"mapped_user_id": mappedUserID,
		}
		logger.Logger.InfoWithFiledCtx(ctx, fields, "用户ID映射成功")
	} else {
		logger.Logger.InfofCtx(ctx, "[用户ID映射] 未找到映射用户, UnionID: %s", unionID)

		// 记录映射未找到的监控日志
		fields := map[string]interface{}{
			"operation":   "user_id_mapping_not_found",
			"union_id":    unionID,
			"new_game_id": config.GlobConfig.UserIDMapping.NewGameID,
			"old_game_id": config.GlobConfig.UserIDMapping.OldGameID,
		}
		logger.Logger.InfoWithFiledCtx(ctx, fields, "用户ID映射未找到")
	}

	return mappedUserID, nil
}

// GetMappedUserID 用户ID映射功能 - 临时过渡逻辑
// TODO: 此功能为临时过渡逻辑，后续需要完全移除
// 当game_id为指定的新游戏ID时，通过union_id查找旧游戏ID下是否存在对应的user_id
// 如果存在，返回旧游戏下的user_id；如果不存在，返回空字符串
// Deprecated: 请使用 GetUserNewStatusAndMappedID 以同时获得新用户判定与映射结果，避免重复查询。
func (s *UserService) GetMappedUserID(ctx context.Context, unionID string, gameID string) (string, error) {
	// 功能开关检查
	if !config.GlobConfig.UserIDMapping.Enabled {
		logger.Logger.DebugfCtx(ctx, "[用户ID映射] 功能已禁用")
		return "", nil
	}

	// 仅对指定的新游戏ID进行映射
	if gameID != config.GlobConfig.UserIDMapping.NewGameID {
		logger.Logger.DebugfCtx(ctx, "[用户ID映射] 当前游戏ID不需要映射: %s", gameID)
		return "", nil
	}

	// 参数验证
	if unionID == "" {
		logger.Logger.DebugfCtx(ctx, "[用户ID映射] UnionID为空，跳过映射")
		return "", nil
	}

	logger.Logger.InfofCtx(ctx, "[用户ID映射] 开始查找映射用户, UnionID: %s, 新游戏ID: %s, 旧游戏ID: %s",
		unionID, config.GlobConfig.UserIDMapping.NewGameID, config.GlobConfig.UserIDMapping.OldGameID)

	// 通过UnionID在旧游戏ID下查找用户
	mappedUserID, err := s.GetUserIDByUnionIDSafe(ctx, unionID, config.GlobConfig.UserIDMapping.OldGameID)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "[用户ID映射] 查找映射用户失败: %v, UnionID: %s", err, unionID)
		return "", nil // 映射失败不影响正常流程
	}

	if mappedUserID != "" {
		logger.Logger.InfofCtx(ctx, "[用户ID映射] 找到映射用户, 旧UserID: %s, UnionID: %s", mappedUserID, unionID)

		// 记录映射成功的监控日志
		fields := map[string]interface{}{
			"operation":      "user_id_mapping_success",
			"union_id":       unionID,
			"new_game_id":    config.GlobConfig.UserIDMapping.NewGameID,
			"old_game_id":    config.GlobConfig.UserIDMapping.OldGameID,
			"mapped_user_id": mappedUserID,
		}
		logger.Logger.InfoWithFiledCtx(ctx, fields, "用户ID映射成功")
	} else {
		logger.Logger.InfofCtx(ctx, "[用户ID映射] 未找到映射用户, UnionID: %s", unionID)

		// 记录映射未找到的监控日志
		fields := map[string]interface{}{
			"operation":   "user_id_mapping_not_found",
			"union_id":    unionID,
			"new_game_id": config.GlobConfig.UserIDMapping.NewGameID,
			"old_game_id": config.GlobConfig.UserIDMapping.OldGameID,
		}
		logger.Logger.InfoWithFiledCtx(ctx, fields, "用户ID映射未找到")
	}

	return mappedUserID, nil
}

// UserMappingStatus 统一返回新用户与旧用户映射状态
type UserMappingStatus struct {
	IsNewUser      bool   // 是否为新用户（针对新游戏ID）
	MappedUserID   string // 旧游戏映射的用户ID（如果存在且功能开启）
	HasOldGameUser bool   // 是否存在旧游戏记录
	HasNewGameUser bool   // 是否存在新游戏记录
}

// GetUserNewStatusAndMappedID 合并 IsNewUserByUnionID 与 GetMappedUserID 的查询以减少重复数据库访问
// 逻辑：
// 1. 通过 unionID -> a_user_minigame 拿 userIDs，若无记录直接视为新用户
// 2. 在 a_user 表一次查出这些 userID 在新旧游戏下的记录
// 3. 解析：是否存在新游戏记录 => IsNewUser；是否存在旧游戏记录且功能开启 => MappedUserID
// 4. 失败返回 error，由上层决定降级
func (s *UserService) GetUserNewStatusAndMappedID(ctx context.Context, unionID, newGameID string) (*UserMappingStatus, error) {
	status := &UserMappingStatus{IsNewUser: true}
	if unionID == "" || newGameID == "" { // 与旧逻辑保持一致：空 union 视为新用户
		logger.Logger.DebugfCtx(ctx, "[UserMapping] 参数为空，unionID=%s, newGameID=%s", unionID, newGameID)
		return status, nil
	}

	oldGameID := config.GlobConfig.UserIDMapping.OldGameID
	featureEnabled := config.GlobConfig.UserIDMapping.Enabled

	userMiniGame := store.QueryDB().AUserMinigame
	userTbl := store.QueryDB().AUser

	// Step1: union -> userIDs
	var userIDs []string
	if err := userMiniGame.WithContext(ctx).
		Select(userMiniGame.UserID).
		Where(userMiniGame.UnionID.Eq(unionID)).
		Where(userMiniGame.IsDeleted.Zero()).
		Scan(&userIDs); err != nil {
		logger.Logger.ErrorfCtx(ctx, "[GetUserNewStatusAndMappedID] 查询 a_user_minigame 失败: %v, unionID=%s", err, unionID)
		return nil, err
	}
	if len(userIDs) == 0 { // 无历史记录 => 新用户
		logger.Logger.InfofCtx(ctx, "[GetUserNewStatusAndMappedID] 无 minigame 记录, 视为新用户, unionID=%s, newGameID=%s", unionID, newGameID)
		return status, nil
	}

	// Step2: 一次性查询 a_user 中新旧游戏分布
	gameIDs := []string{newGameID}
	if oldGameID != "" && oldGameID != newGameID { // 避免重复
		gameIDs = append(gameIDs, oldGameID)
	}

	type userRow struct {
		UserID string `gorm:"column:user_id"`
		GameID string `gorm:"column:game_id"`
	}
	var rows []userRow
	if err := userTbl.WithContext(ctx).
		Select(userTbl.UserID, userTbl.GameID).
		Where(userTbl.UserID.In(userIDs...)).
		Where(userTbl.GameID.In(gameIDs...)).
		Where(userTbl.IsDeleted.Zero()).
		Scan(&rows); err != nil {
		logger.Logger.ErrorfCtx(ctx, "[GetUserNewStatusAndMappedID] 查询 a_user 失败: %v, unionID=%s", err, unionID)
		return nil, err
	}

	status.IsNewUser = true
	for _, r := range rows {
		if r.GameID == newGameID {
			status.HasNewGameUser = true
			status.IsNewUser = false
		} else if featureEnabled && r.GameID == oldGameID && status.MappedUserID == "" {
			status.HasOldGameUser = true
			status.MappedUserID = r.UserID // 取第一条
		}
	}

	if !featureEnabled { // 功能关闭不暴露映射结果
		status.MappedUserID = ""
	}

	fields := map[string]interface{}{
		"operation":         "user_mapping_status",
		"union_id":          unionID,
		"new_game_id":       newGameID,
		"old_game_id":       oldGameID,
		"is_new_user":       status.IsNewUser,
		"mapped_user_id":    status.MappedUserID,
		"has_old_game_user": status.HasOldGameUser,
		"has_new_game_user": status.HasNewGameUser,
		"feature_enabled":   featureEnabled,
	}
	logger.Logger.InfoWithFiledCtx(ctx, fields, "[UserMapping] 状态结果")

	return status, nil
}

// GetUserNewStatusAndMappedIDCached 带 Redis + L1 缓存包装
// 缓存设计：
//   - Key: user_mapping_status:{unionID}:{newGameID}
//   - Value: JSON(UserMappingStatus)
//   - L1 先查 -> Redis 再查 -> DB 回源
//   - 不对功能开关进行缓存裁剪，开关在逻辑使用时再次判断（避免开关切换时大面积失效操作）
func (s *UserService) GetUserNewStatusAndMappedIDCached(ctx context.Context, unionID, newGameID string) (*UserMappingStatus, error) {
	cacheKey := userMappingCacheKey(unionID, newGameID)

	if val, err := redis.Redis().Get(ctx, cacheKey).Result(); err == nil && val != "" {
		if st, uErr := unmarshalStatus(val); uErr == nil {
			return st, nil
		} else {
			_ = redis.Redis().Del(ctx, cacheKey).Err()
		}
	}

	st, err := s.GetUserNewStatusAndMappedID(ctx, unionID, newGameID)
	if err != nil {
		return nil, err
	}

	if jsonStr, mErr := marshalStatus(st); mErr == nil {
		// 选用与用户存在性缓存一致的 5 分钟 TTL，若后续需调整可引入单独常量
		if rErr := redis.Set(ctx, cacheKey, jsonStr, time.Duration(constants.RedisUserExistsExpire)*time.Second); rErr != nil {
			logger.Logger.WarnfCtx(ctx, "[GetUserNewStatusAndMappedIDCached] Redis 写入失败 key=%s err=%v", cacheKey, rErr)
		}
	}
	return st, nil
}

// GetMappedUserChannelInfo 获取映射用户的Channel和CreateAt信息
// 用于用户ID映射逻辑中获取映射用户的渠道信息
func (s *UserService) GetMappedUserChannelInfo(ctx context.Context, mappedUserID string) (*bean.MappedUserChannelInfo, error) {
	if mappedUserID == "" {
		return nil, fmt.Errorf("mappedUserID不能为空")
	}

	user := store.QueryDB().AUser
	userInfo, err := user.WithContext(ctx).
		Select(user.Channel, user.AdFrom, user.CreatedAt).
		Where(user.UserID.Eq(mappedUserID)).
		Where(user.IsDeleted.Zero()).
		First()

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Logger.WarnfCtx(ctx, "[GetMappedUserChannelInfo] 未找到映射用户信息: %s", mappedUserID)
			return nil, fmt.Errorf("未找到映射用户信息: %s", mappedUserID)
		}
		logger.Logger.ErrorfCtx(ctx, "[GetMappedUserChannelInfo] 查询映射用户信息失败: %v, userID=%s", err, mappedUserID)
		return nil, fmt.Errorf("查询映射用户信息失败: %w", err)
	}

	logger.Logger.InfofCtx(ctx, "[GetMappedUserChannelInfo] 成功获取映射用户渠道信息, userID: %s, channel: %s, adFrom: %s",
		mappedUserID, userInfo.Channel, userInfo.AdFrom)

	return &bean.MappedUserChannelInfo{
		Channel:   userInfo.Channel,
		ADFrom:    userInfo.AdFrom,
		CreatedAt: userInfo.CreatedAt,
	}, nil
}

// IsNewUserByUnionID 检查指定的UnionID在特定游戏中是否为新用户（在a_user_minigame表中无历史记录）
// 用于用户ID映射逻辑中的新用户检测
func (s *UserService) IsNewUserByUnionID(ctx context.Context, unionID string, gameID string) (bool, error) {
	if unionID == "" {
		return true, nil // 空UnionID视为新用户
	}

	userMiniGame := store.QueryDB().AUserMinigame
	user := store.QueryDB().AUser

	// 检查 a_user_minigame 表中是否存在该 union_id 在指定游戏中的记录
	// 先通过 union_id 查询用户ID列表
	var userIDs []string
	err := userMiniGame.WithContext(ctx).
		Select(userMiniGame.UserID).
		Where(userMiniGame.UnionID.Eq(unionID)).
		Where(userMiniGame.IsDeleted.Zero()).
		Scan(&userIDs)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[IsNewUserByUnionID] 查询a_user_minigame表失败: %v, UnionID: %s", err, unionID)
		return false, err
	}

	if len(userIDs) == 0 {
		logger.Logger.InfofCtx(ctx, "[IsNewUserByUnionID] 在a_user_minigame表中未找到记录, 判定为新用户, UnionID: %s, GameID: %s", unionID, gameID)
		return true, nil // 未找到记录，是新用户
	}

	// 再验证这些用户ID中是否有属于指定游戏的记录
	var count int64
	count, err = user.WithContext(ctx).
		Where(user.UserID.In(userIDs...)).
		Where(user.GameID.Eq(gameID)).
		Where(user.IsDeleted.Zero()).
		Count()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[IsNewUserByUnionID] 查询a_user表失败: %v, UnionID: %s, GameID: %s", err, unionID, gameID)
		return false, err
	}

	if count > 0 {
		logger.Logger.DebugfCtx(ctx, "[IsNewUserByUnionID] 在指定游戏中找到用户记录, 不是新用户, UnionID: %s, GameID: %s, 记录数: %d", unionID, gameID, count)
		return false, nil // 找到记录，不是新用户
	}

	// 在指定游戏中没有找到记录，判定为新用户
	logger.Logger.InfofCtx(ctx, "[IsNewUserByUnionID] 在指定游戏中未找到记录, 判定为新用户, UnionID: %s, GameID: %s", unionID, gameID)
	return true, nil
}

// GetUserIDByUnionIDSafe 通过UnionID获取UserID，安全版本，在查不到用户时不报错而是返回空字符串
func (s *UserService) GetUserIDByUnionIDSafe(ctx context.Context, unionID string, gameID string) (string, error) {
	userMiniGame := store.QueryDB().AUserMinigame
	user := store.QueryDB().AUser

	// 如果unionID或gameID为空，直接返回空字符串
	if unionID == "" || gameID == "" {
		return "", nil
	}

	// 先通过 union_id 查询，结果集很小
	var userIDs []string
	err := userMiniGame.WithContext(ctx).
		Select(userMiniGame.UserID).
		Where(userMiniGame.UnionID.Eq(unionID)).
		Where(userMiniGame.IsDeleted.Zero()).
		Scan(&userIDs)

	if err != nil {
		logger.Logger.WarnfCtx(ctx, "通过UnionID查询用户ID失败: %v", err)
		return "", nil
	}

	if len(userIDs) == 0 {
		logger.Logger.WarnfCtx(ctx, "未找到与UnionID关联的用户: %s", unionID)
		return "", nil
	}

	// 再验证 game_id
	var userID string
	err = user.WithContext(ctx).
		Select(user.UserID).
		Where(user.UserID.In(userIDs...)).
		Where(user.GameID.Eq(gameID)).
		Where(user.IsDeleted.Zero()).
		Limit(1).
		Pluck(user.UserID, &userID)

	if err != nil {
		logger.Logger.WarnfCtx(ctx, "通过GameID验证用户失败: %v", err)
		return "", nil
	}

	if userID == "" {
		logger.Logger.WarnfCtx(ctx, "未找到与游戏ID关联的用户: unionID=%s, gameID=%s", unionID, gameID)
		return "", nil
	}

	return userID, nil
}

// GetUserIDsByUnionIDSafe 通过UnionID批量获取多个GameID下的UserID，安全版本
// 返回 map[gameID]userID，仅查询存在且未删除的记录；查询不到的 gameID 不会出现在 map 中
// 实现方式：
//  1. unionID -> a_user_minigame 查询所有相关 user_id（一次）
//  2. a_user 表以 user_id IN (...) AND game_id IN (目标集合) 过滤
//
// 相比多次 GetUserIDByUnionIDSafe，可减少重复的 unionID 反查与多次往返
func (s *UserService) GetUserIDsByUnionIDSafe(ctx context.Context, unionID string, gameIDs []string) (map[string]string, error) {
	userMiniGame := store.QueryDB().AUserMinigame
	user := store.QueryDB().AUser

	// 参数校验
	if unionID == "" || len(gameIDs) == 0 {
		return map[string]string{}, nil
	}

	// 去重 gameIDs
	uniq := make([]string, 0, len(gameIDs))
	seen := make(map[string]struct{}, len(gameIDs))
	for _, g := range gameIDs {
		if g == "" { // 跳过空
			continue
		}
		if _, ok := seen[g]; ok {
			continue
		}
		seen[g] = struct{}{}
		uniq = append(uniq, g)
	}
	if len(uniq) == 0 {
		return map[string]string{}, nil
	}

	// Step1: unionID -> userIDs（结果通常很小）
	var userIDs []string
	if err := userMiniGame.WithContext(ctx).
		Select(userMiniGame.UserID).
		Where(userMiniGame.UnionID.Eq(unionID)).
		Where(userMiniGame.IsDeleted.Zero()).
		Scan(&userIDs); err != nil {
		logger.Logger.WarnfCtx(ctx, "[GetUserIDsByUnionIDSafe] 通过UnionID查询用户列表失败: %v", err)
		return map[string]string{}, nil // 安全版本：出错返回空
	}
	if len(userIDs) == 0 {
		return map[string]string{}, nil
	}

	// Step2: 过滤出目标 gameID 的 userID 记录
	type row struct {
		GameID string `gorm:"column:game_id"`
		UserID string `gorm:"column:user_id"`
	}
	var rows []row
	if err := user.WithContext(ctx).
		Select(user.GameID, user.UserID).
		Where(user.UserID.In(userIDs...)).
		Where(user.GameID.In(uniq...)).
		Where(user.IsDeleted.Zero()).
		Scan(&rows); err != nil {
		logger.Logger.WarnfCtx(ctx, "[GetUserIDsByUnionIDSafe] 通过GameID集合查询失败: %v", err)
		return map[string]string{}, nil // 安全版本：出错返回空
	}

	result := make(map[string]string, len(rows))
	for _, r := range rows {
		// 若同一 gameID 有多条，只保留第一条即可（与单查逻辑一致）
		if _, exists := result[r.GameID]; !exists {
			result[r.GameID] = r.UserID
		}
	}
	return result, nil
}

// GetUserIDByDouyinOpenIDSafe 通过抖音OpenID获取UserID，安全版本，在查不到用户时不报错而是返回空字符串
func (s *UserService) GetUserIDByDouyinOpenIDSafe(ctx context.Context, douyinOpenID string) (string, error) {
	// 如果douyinOpenID为空，直接返回空字符串
	if douyinOpenID == "" {
		return "", nil
	}

	userDouyin := store.QueryDB().AUserDouyin
	userInfo, err := userDouyin.WithContext(ctx).
		Where(userDouyin.OpenID.Eq(douyinOpenID)).
		Where(userDouyin.IsDeleted.Zero()).
		First()

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Logger.WarnfCtx(ctx, "未找到与抖音OpenID关联的用户: %s", douyinOpenID)
			return "", nil
		}
		// 区分context相关错误和真正的系统错误
		if errors.Is(err, context.Canceled) {
			logger.Logger.InfofCtx(ctx, "[GetUserIDByDouyinOpenIDSafe] 查询被客户端取消, douyinOpenID: %s", douyinOpenID)
		} else if errors.Is(err, context.DeadlineExceeded) {
			logger.Logger.WarnfCtx(ctx, "[GetUserIDByDouyinOpenIDSafe] 查询超时, douyinOpenID: %s", douyinOpenID)
		} else {
			logger.Logger.WarnfCtx(ctx, "[GetUserIDByDouyinOpenIDSafe] 通过抖音OpenID查询用户ID失败: %v, douyinOpenID: %s", err, douyinOpenID)
		}
		return "", nil
	}

	logger.Logger.InfofCtx(ctx, "[GetUserIDByDouyinOpenIDSafe] 查询成功, douyinOpenID: %s, userID: %s", douyinOpenID, userInfo.UserID)
	return userInfo.UserID, nil
}

// 弃用: IsNewUser 检查用户是否为新用户（通过OpenID判断）
func (s *UserService) IsNewUser(ctx context.Context, openID string) (bool, error) {
	// 参数验证
	if openID == "" {
		return false, fmt.Errorf("openID不能为空")
	}

	start := time.Now()

	// 优化：使用EXISTS查询替代COUNT，性能更好
	userMiniGame := store.QueryDB().AUserMinigame
	var userID string
	err := userMiniGame.WithContext(ctx).
		Select(userMiniGame.UserID).
		Where(userMiniGame.IsDeleted.Zero()).
		Where(userMiniGame.OpenID.Eq(openID)).
		Limit(1).
		Pluck(userMiniGame.UserID, &userID)

	// 计算查询耗时
	elapsed := time.Since(start)
	latencyMillis := float64(elapsed.Nanoseconds()) / 1e6

	// 记录性能日志
	fields := map[string]interface{}{
		"latency_millis": fmt.Sprintf("%.3f", latencyMillis),
		"operation":      "new_user_check",
		"open_id":        openID,
	}

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		fields["error"] = err.Error()
		logger.Logger.InfoWithFiledCtx(ctx, fields, "新用户检查失败")
		return false, err
	}

	// 判断是否为新用户（如果没找到记录或发生ErrRecordNotFound，则为新用户）
	isNewUser := userID == "" || errors.Is(err, gorm.ErrRecordNotFound)

	fields["is_new_user"] = isNewUser
	fields["found_user_id"] = userID
	logger.Logger.InfoWithFiledCtx(ctx, fields, "新用户检查耗时")

	return isNewUser, nil
}

// IsNewUserWithCache 检查用户是否为新用户（带Redis缓存优化）
func (s *UserService) IsNewUserWithCache(ctx context.Context, openID, gameID string) (bool, error) {
	// 参数验证
	if openID == "" {
		return false, fmt.Errorf("openID不能为空")
	}
	if gameID == "" {
		return false, fmt.Errorf("gameID不能为空")
	}
	// 基础安全检查：防止异常长度的参数
	if len(openID) > 255 || len(gameID) > 255 {
		return false, fmt.Errorf("参数长度超出限制")
	}

	// 使用常量定义的缓存键格式
	cacheKey := fmt.Sprintf(constants.RedisUserExistsKey, gameID, openID)

	// 先尝试从缓存获取
	cacheResult, err := redis.Redis().Get(ctx, cacheKey).Result()

	if err == nil {
		// 缓存命中，使用tagged switch处理不同的缓存值
		switch cacheResult {
		case "0":
			// 新用户
			return true, nil

		case "1":
			// 老用户
			return false, nil

		default:
			// 缓存值异常，清除缓存并查询数据库
			logger.Logger.WarnfCtx(ctx, "[IsNewUserWithCache] 用户存在性缓存值异常: %s, 清除缓存", cacheResult)
			if delErr := redis.Redis().Del(ctx, cacheKey).Err(); delErr != nil {
				logger.Logger.WarnfCtx(ctx, "[IsNewUserWithCache] 清除异常缓存失败: %v", delErr)
			}
			// 继续执行数据库查询逻辑
		}
	}

	// 缓存未命中，使用分布式锁防止惊群效应
	lockKey := fmt.Sprintf("cache:user_exists_lock:%s:%s", gameID, openID)
	isLocked := redis.Lock(ctx, lockKey, 30*time.Second)

	if isLocked {
		// 获得锁，执行数据库查询和缓存更新
		defer redis.UnLock(ctx, lockKey)

		// 双重检查：再次尝试从缓存获取，可能其他请求已经更新了缓存
		if doubleCheckResult, doubleCheckErr := redis.Redis().Get(ctx, cacheKey).Result(); doubleCheckErr == nil {
			switch doubleCheckResult {
			case "0":
				// 新用户
				return true, nil
			case "1":
				// 老用户
				return false, nil
			}
		}

		// 执行数据库查询
		db := store.GOrmDB(ctx)
		var userID string
		err = db.Raw(`
			SELECT um.user_id
			FROM a_user_minigame um
			LEFT JOIN a_user u ON um.user_id = u.user_id AND u.is_deleted = 0
			WHERE um.open_id = ? AND um.is_deleted = 0 AND u.game_id = ?
			LIMIT 1
		`, openID, gameID).Scan(&userID).Error

		if err != nil {
			return false, err
		}

		// 判断是否为新用户
		isNewUser := userID == ""

		// 写入缓存（TTL 5分钟）
		cacheValue := "1" // 老用户
		if isNewUser {
			cacheValue = "0" // 新用户
		}
		if cacheErr := redis.Redis().Set(ctx, cacheKey, cacheValue, time.Duration(constants.RedisUserExistsExpire)*time.Second).Err(); cacheErr != nil {
			// 缓存写入失败不影响主业务逻辑，仅记录警告日志
			logger.Logger.WarnfCtx(ctx, "[IsNewUserWithCache] 缓存写入失败, key: %s, error: %v", cacheKey, cacheErr)
		}

		return isNewUser, nil
	} else {
		// 未获得锁，等待一小段时间后重试缓存
		time.Sleep(10 * time.Millisecond)
		if retryResult, retryErr := redis.Redis().Get(ctx, cacheKey).Result(); retryErr == nil {
			switch retryResult {
			case "0":
				// 新用户
				return true, nil
			case "1":
				// 老用户
				return false, nil
			}
		}

		// 如果重试后仍然没有缓存，直接查询数据库（降级处理）
		logger.Logger.WarnfCtx(ctx, "[IsNewUserWithCache] 获取用户存在性锁失败，降级为直接查询, gameID: %s, openID: %s", gameID, openID)

		db := store.GOrmDB(ctx)
		var userID string
		err = db.Raw(`
			SELECT um.user_id
			FROM a_user_minigame um
			LEFT JOIN a_user u ON um.user_id = u.user_id AND u.is_deleted = 0
			WHERE um.open_id = ? AND um.is_deleted = 0 AND u.game_id = ?
			LIMIT 1
		`, openID, gameID).Scan(&userID).Error

		if err != nil {
			return false, err
		}

		// 判断是否为新用户
		isNewUser := userID == ""

		// 异步更新缓存，避免下次降级（在锁获取失败的降级处理中）
		go func() {
			time.Sleep(100 * time.Millisecond) // 稍作延时避免竞争
			cacheValue := "1"                  // 老用户
			if isNewUser {
				cacheValue = "0" // 新用户
			}
			if asyncErr := redis.Redis().Set(context.Background(), cacheKey, cacheValue, time.Duration(constants.RedisUserExistsExpire)*time.Second).Err(); asyncErr != nil {
				logger.Logger.WarnfCtx(context.Background(), "[IsNewUserWithCache] 降级处理异步缓存更新失败, key: %s, error: %v", cacheKey, asyncErr)
			}
		}()

		return isNewUser, nil
	}
}

// UpdateUserExistsCache 更新用户存在性缓存（用于用户注册后的缓存一致性维护）
func (s *UserService) UpdateUserExistsCache(ctx context.Context, openID, gameID string, isNewUser bool) {
	if openID == "" || gameID == "" {
		return // 参数无效，跳过缓存更新
	}

	// 使用常量定义的缓存键格式
	cacheKey := fmt.Sprintf(constants.RedisUserExistsKey, gameID, openID)
	cacheValue := "1" // 老用户
	if isNewUser {
		cacheValue = "0" // 新用户
	}

	// 异步更新缓存，不阻塞主流程
	go func() {
		if err := redis.Redis().Set(context.Background(), cacheKey, cacheValue, time.Duration(constants.RedisUserExistsExpire)*time.Second).Err(); err != nil {
			logger.Logger.WarnfCtx(ctx, "[UpdateUserExistsCache] 缓存更新失败, key: %s, error: %v", cacheKey, err)
		}
	}()
}

// SaveDouyinWebAuthInfo 保存抖音Web用户公开信息到AUserDouyinWeb表（新表结构，无令牌字段）
// 注意：新表使用 (client_key, open_id) 作为联合主键，且不包含 access_token/refresh_token 等认证字段。
func (s *UserService) SaveDouyinWebAuthInfo(ctx context.Context, clientKey string, tokenResp *bean.DouyinAccessTokenResp, userInfo *bean.DouyinUserInfo) error {
	if tokenResp == nil {
		return fmt.Errorf("tokenResp不能为空")
	}
	if clientKey == "" {
		return fmt.Errorf("clientKey不能为空")
	}

	userDouyinWeb := store.QueryDB().AUserDouyinWeb

	// 仅按新表字段构建模型（无令牌字段、无时间戳/删除标记字段）
	userWebModel := &model.AUserDouyinWeb{
		ClientKey: clientKey,
		OpenID:    tokenResp.Data.OpenID,
	}

	// 如果有用户信息，映射可用字段（avatar_url -> avatar，nickname保持不变，union_id可选）
	if userInfo != nil {
		userWebModel.UnionID = userInfo.UnionID
		userWebModel.Nickname = userInfo.NickName
		userWebModel.Avatar = userInfo.AvatarURL
		// EAccountRole 需通过企业号开放API另行获取，这里不设置
	}

	// 使用联合主键(client_key, open_id)判断存在性
	existingRecord, err := userDouyinWeb.WithContext(ctx).
		Where(userDouyinWeb.ClientKey.Eq(clientKey)).
		Where(userDouyinWeb.OpenID.Eq(tokenResp.Data.OpenID)).
		First()

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Logger.ErrorfCtx(ctx, "[SaveDouyinWebAuthInfo] 查询现有记录失败: %v, client_key: %s, open_id: %s", err, clientKey, tokenResp.Data.OpenID)
		return fmt.Errorf("查询现有记录失败: %w", err)
	}

	if existingRecord != nil {
		// 记录已存在，仅更新可用的公开字段
		updates := map[string]interface{}{}
		if userInfo != nil {
			if userInfo.UnionID != "" {
				updates["union_id"] = userInfo.UnionID
			}
			if userInfo.NickName != "" {
				updates["nickname"] = userInfo.NickName
			}
			if userInfo.AvatarURL != "" {
				updates["avatar"] = userInfo.AvatarURL
			}
		}
		if len(updates) > 0 {
			_, err = userDouyinWeb.WithContext(ctx).
				Where(userDouyinWeb.ClientKey.Eq(clientKey)).
				Where(userDouyinWeb.OpenID.Eq(tokenResp.Data.OpenID)).
				Updates(updates)
			if err != nil {
				logger.Logger.ErrorfCtx(ctx, "[SaveDouyinWebAuthInfo] 更新抖音Web用户信息失败: %v, client_key: %s, open_id: %s", err, clientKey, tokenResp.Data.OpenID)
				return fmt.Errorf("更新抖音Web用户信息失败: %w", err)
			}
		}
	} else {
		// 记录不存在，创建新记录
		if err = userDouyinWeb.WithContext(ctx).Create(userWebModel); err != nil {
			logger.Logger.ErrorfCtx(ctx, "[SaveDouyinWebAuthInfo] 创建抖音Web用户信息失败: %v, client_key: %s, open_id: %s", err, clientKey, tokenResp.Data.OpenID)
			return fmt.Errorf("创建抖音Web用户信息失败: %w", err)
		}
	}

	logger.Logger.InfofCtx(ctx, "[SaveDouyinWebAuthInfo] 成功保存抖音Web用户信息, client_key: %s, open_id: %s", clientKey, tokenResp.Data.OpenID)
	return nil
}

// GetUserInfoByDouyinUnionID 通过抖音UnionID在a_user_douyin表中查找用户信息
func (s *UserService) GetUserInfoByDouyinUnionID(ctx context.Context, unionID string) (*model.AUserDouyin, error) {
	if unionID == "" {
		return nil, fmt.Errorf("unionID不能为空")
	}

	userDouyin := store.QueryDB().AUserDouyin
	userInfo, err := userDouyin.WithContext(ctx).
		Where(userDouyin.UnionID.Eq(unionID)).
		Where(userDouyin.IsDeleted.Is(false)).
		First()

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Logger.InfofCtx(ctx, "[GetUserInfoByDouyinUnionID] 未找到与UnionID关联的用户: %s", unionID)
			return nil, nil // 返回nil而不是错误，表示未找到用户
		}
		logger.Logger.ErrorfCtx(ctx, "[GetUserInfoByDouyinUnionID] 通过UnionID查询用户失败: %v, UnionID: %s", err, unionID)
		return nil, fmt.Errorf("通过UnionID查询用户失败: %w", err)
	}

	logger.Logger.InfofCtx(ctx, "[GetUserInfoByDouyinUnionID] 成功找到用户, UserID: %s, UnionID: %s", userInfo.UserID, unionID)
	return userInfo, nil
}
