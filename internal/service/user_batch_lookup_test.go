//go:build integration
// +build integration

package service

import (
	"context"
	"testing"
)

// 仅测试入参边界与返回 map 为空场景（由于未连接真实数据库与测试数据）
func TestGetUserIDsByUnionIDSafe_ParamEdge(t *testing.T) {
	ctx := context.Background()
	s := &UserService{}

	cases := []struct {
		name    string
		unionID string
		gameIDs []string
		expect  int
	}{
		{"空unionID", "", []string{"a", "b"}, 0},
		{"空gameIDs", "u1", []string{}, 0},
		{"含空gameID去重", "u1", []string{"", "a", "a"}, 0},
	}

	for _, c := range cases {
		res, err := s.GetUserIDsByUnionIDSafe(ctx, c.unionID, c.gameIDs)
		if err != nil { // 安全版本不应返回错误
			t.Fatalf("%s 返回错误: %v", c.name, err)
		}
		if len(res) != c.expect {
			t.<PERSON><PERSON>("%s 期望长度=%d 实际=%d", c.name, c.expect, len(res))
		}
	}
}
