package service

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"sync"
	"time"

	"net/http"
	"net/url"
	"strings"

	"git.panlonggame.com/bkxplatform/admin-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"

	// 上传到腾讯云COSadmin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/tencentyun/cos-go-sdk-v5"
	"gorm.io/gorm"
)

var (
	_uploadOnce    sync.Once
	_uploadService *UploadService
)

type UploadService struct{}

func SingletonUploadService() *UploadService {
	_uploadOnce.Do(func() {
		_uploadService = &UploadService{}
	})
	return _uploadService
}

// UploadFile 上传文件
func (s *UploadService) UploadFile(ctx context.Context, file *multipart.FileHeader) (string, error) {
	stream, err := file.Open()
	if err != nil {
		return "", err
	}
	defer stream.Close()

	// stream to []byte
	streamByte, err := io.ReadAll(stream)
	if err != nil {
		return "", err
	}
	md5 := util.EncodeMD5(string(streamByte))

	// 检查文件是否已存在
	fileUrl, err := s.GetFileUrl(ctx, md5)
	if err != nil {
		return "", err
	}
	if fileUrl != "" {
		return fileUrl, nil
	}

	// 上传到腾讯云COS
	u, _ := url.Parse(config.GlobConfig.OSS.BucketURL)
	b := &cos.BaseURL{BucketURL: u}
	client := cos.NewClient(b, &http.Client{
		Transport: &cos.AuthorizationTransport{
			SecretID:  config.GlobConfig.OSS.SecretID,
			SecretKey: config.GlobConfig.OSS.SecretKey,
		},
	})

	// 使用时间戳+随机数作为文件名前缀
	filePrefix := fmt.Sprintf("%d%d", time.Now().Unix(), util.GenRandomNum())
	fileName := fmt.Sprintf("/%s/%s-%s", config.GlobConfig.OSS.Env, filePrefix, file.Filename)
	_, err = client.Object.Put(ctx, fileName, bytes.NewReader(streamByte), nil)
	if err != nil {
		return "", err
	}

	// 构建文件URL
	fileURL := fmt.Sprintf("%s%s", config.GlobConfig.OSS.Domain, fileName)
	ossURL := fmt.Sprintf("%s%s", config.GlobConfig.OSS.BucketURL, fileName)

	// 保存文件信息到数据库
	err = s.CreateFileData(ctx, fileName, md5, fileURL, ossURL, file.Size)
	if err != nil {
		return "", err
	}

	return fileURL, nil
}

// UploadImageFromURL 从URL下载图片并上传到OSS
// func (s *UploadService) UploadImageFromURL(ctx context.Context, imageURL, fileName string) (string, error) {
// 	logger.Logger.InfofCtx(ctx, "[人脸融合] 开始从URL下载并上传图片, URL: %s, 文件名: %s", imageURL, fileName)

// 	// 从URL下载图片数据
// 	resp, err := http.Get(imageURL)
// 	if err != nil {
// 		logger.Logger.ErrorfCtx(ctx, "[人脸融合] 下载图片失败: %v", err)
// 		return "", fmt.Errorf("download image from URL: %w", err)
// 	}
// 	defer func() {
// 		if closeErr := resp.Body.Close(); closeErr != nil {
// 			logger.Logger.ErrorfCtx(ctx, "Failed to close response body: %v", closeErr)
// 		}
// 	}()

// 	if resp.StatusCode != http.StatusOK {
// 		logger.Logger.ErrorfCtx(ctx, "[人脸融合] 下载图片HTTP状态错误: %d", resp.StatusCode)
// 		return "", fmt.Errorf("download image failed with status: %d", resp.StatusCode)
// 	}

// 	imageData, err := io.ReadAll(resp.Body)
// 	if err != nil {
// 		logger.Logger.ErrorfCtx(ctx, "[人脸融合] 读取图片数据失败: %v", err)
// 		return "", fmt.Errorf("read image data: %w", err)
// 	}

// 	// 计算MD5
// 	md5 := util.EncodeMD5(string(imageData))

// 	// 检查文件是否已存在
// 	fileUrl, err := s.GetFileUrl(ctx, md5)
// 	if err != nil {
// 		logger.Logger.ErrorfCtx(ctx, "[人脸融合] 检查文件是否存在失败: %v", err)
// 		return "", err
// 	}
// 	if fileUrl != "" {
// 		logger.Logger.InfofCtx(ctx, "[人脸融合] 文件已存在，返回已有URL: %s", fileUrl)
// 		return fileUrl, nil
// 	}

// 	// 使用时间戳+随机数作为文件名前缀
// 	filePrefix := fmt.Sprintf("%d%d", time.Now().Unix(), util.GenRandomNum())
// 	ossFileName := fmt.Sprintf("/%s/face-fusion/%s-%s", config.GlobConfig.OSS.Env, filePrefix, fileName)

// 	// 上传到腾讯云COS
// 	u, _ := url.Parse(config.GlobConfig.OSS.BucketURL)
// 	b := &cos.BaseURL{BucketURL: u}
// 	client := cos.NewClient(b, &http.Client{
// 		Transport: &cos.AuthorizationTransport{
// 			SecretID:  config.GlobConfig.OSS.SecretID,
// 			SecretKey: config.GlobConfig.OSS.SecretKey,
// 		},
// 	})

// 	// 构建上传选项
// 	putOptions := &cos.ObjectPutOptions{}

// 	_, err = client.Object.Put(ctx, ossFileName, bytes.NewReader(imageData), putOptions)
// 	if err != nil {
// 		logger.Logger.ErrorfCtx(ctx, "[人脸融合] 上传到OSS失败: %v", err)
// 		return "", fmt.Errorf("upload to OSS: %w", err)
// 	}

// 	// 构建文件URL
// 	fileURL := fmt.Sprintf("%s%s", config.GlobConfig.OSS.Domain, ossFileName)
// 	ossURL := fmt.Sprintf("%s%s", config.GlobConfig.OSS.BucketURL, ossFileName)

// 	// 保存文件信息到数据库
// 	err = s.CreateFaceFusionFileData(ctx, ossFileName, md5, fileURL, ossURL, int64(len(imageData)))
// 	if err != nil {
// 		logger.Logger.ErrorfCtx(ctx, "[人脸融合] 保存文件记录失败: %v", err)
// 		return "", fmt.Errorf("save file record: %w", err)
// 	}

// 	logger.Logger.InfofCtx(ctx, "[人脸融合] 从URL上传图片成功, 源URL: %s, 新文件URL: %s", imageURL, fileURL)
// 	return fileURL, nil
// }

// UploadImageFromURLAndGetPresignedURL 从URL下载图片，上传到OSS，并返回预签名下载URL
func (s *UploadService) UploadImageFromURLAndGetPresignedURL(ctx context.Context, imageURL, fileName string, expiration time.Duration) (string, error) {
	logger.Logger.InfofCtx(ctx, "[人脸融合] 开始从URL下载并上传图片，生成预签名URL: URL=%s, 文件名=%s", imageURL, fileName)

	// 从URL下载图片数据
	resp, err := http.Get(imageURL)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[人脸融合] 下载图片失败: %v", err)
		return "", fmt.Errorf("download image from URL: %w", err)
	}
	defer func() {
		if closeErr := resp.Body.Close(); closeErr != nil {
			logger.Logger.ErrorfCtx(ctx, "Failed to close response body: %v", closeErr)
		}
	}()

	if resp.StatusCode != http.StatusOK {
		logger.Logger.ErrorfCtx(ctx, "[人脸融合] 下载图片HTTP状态错误: %d", resp.StatusCode)
		return "", fmt.Errorf("download image failed with status: %d", resp.StatusCode)
	}

	imageData, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[人脸融合] 读取图片数据失败: %v", err)
		return "", fmt.Errorf("read image data: %w", err)
	}

	// 计算MD5
	md5 := util.EncodeMD5(string(imageData))

	// 直接上传到face-fusion/路径，不进行MD5检查
	// 使用时间戳+随机数作为文件名前缀
	filePrefix := fmt.Sprintf("%d%d", time.Now().Unix(), util.GenRandomNum())
	ossFileName := fmt.Sprintf("face-fusion/%s-%s", filePrefix, fileName)

	// 上传到腾讯云COS
	u, _ := url.Parse(config.GlobConfig.OSS.BucketURL)
	b := &cos.BaseURL{BucketURL: u}
	client := cos.NewClient(b, &http.Client{
		Transport: &cos.AuthorizationTransport{
			SecretID:  config.GlobConfig.OSS.SecretID,
			SecretKey: config.GlobConfig.OSS.SecretKey,
		},
	})

	// 构建上传选项
	putOptions := &cos.ObjectPutOptions{}

	_, err = client.Object.Put(ctx, ossFileName, bytes.NewReader(imageData), putOptions)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[人脸融合] 上传到OSS失败: %v", err)
		return "", fmt.Errorf("upload to OSS: %w", err)
	}

	// 构建文件URL
	fileURL := fmt.Sprintf("%s%s", config.GlobConfig.OSS.Domain, ossFileName)
	ossURL := fmt.Sprintf("%s%s", config.GlobConfig.OSS.BucketURL, ossFileName)

	// 保存文件信息到数据库
	err = s.CreateFaceFusionFileData(ctx, ossFileName, md5, fileURL, ossURL, int64(len(imageData)))
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[人脸融合] 保存文件记录失败: %v", err)
		return "", fmt.Errorf("save file record: %w", err)
	}

	logger.Logger.InfofCtx(ctx, "[人脸融合] 图片上传成功: %s", ossFileName)

	// 生成预签名下载URL
	presignedURL, err := s.GetPresignedDownloadURL(ctx, ossFileName, expiration)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[人脸融合] 生成预签名下载URL失败: %v", err)
		return "", fmt.Errorf("generate presigned download URL: %w", err)
	}

	logger.Logger.InfofCtx(ctx, "[人脸融合] 从URL上传图片并生成预签名URL成功, 源URL=%s, 预签名URL=%s", imageURL, presignedURL)
	return presignedURL, nil
}

// GeneratePresignedUploadURL 专门用于生成预签名上传URL的方法
// 适用于客户端需要直接上传文件的场景
func (s *UploadService) GeneratePresignedUploadURL(ctx context.Context, fileName string, expiration time.Duration) (string, error) {
	logger.Logger.InfofCtx(ctx, "[预签名URL] 开始生成预签名上传URL: fileName=%s, expiration=%v", fileName, expiration)

	// 使用时间戳+随机数作为文件名前缀（如果文件名不包含路径）
	var ossFileName string
	if fileName[0] != '/' {
		filePrefix := fmt.Sprintf("%d%d", time.Now().Unix(), util.GenRandomNum())
		ossFileName = fmt.Sprintf("/%s/uploads/%s-%s", config.GlobConfig.OSS.Env, filePrefix, fileName)
	} else {
		ossFileName = fileName
	}

	// 生成预签名URL
	presignedURL, err := s.GetPresignedUploadURL(ctx, ossFileName, expiration)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[预签名URL] 生成预签名URL失败: %v", err)
		return "", fmt.Errorf("generate presigned URL: %w", err)
	}

	logger.Logger.InfofCtx(ctx, "[预签名URL] 生成预签名URL成功: fileName=%s, presignedURL=%s", fileName, presignedURL)
	return presignedURL, nil
}

// GetPresignedUploadURL 生成预签名上传URL
func (s *UploadService) GetPresignedUploadURL(ctx context.Context, fileName string, expiration time.Duration) (string, error) {
	// 初始化COS客户端
	u, _ := url.Parse(config.GlobConfig.OSS.BucketURL)
	b := &cos.BaseURL{BucketURL: u}
	client := cos.NewClient(b, &http.Client{
		Transport: &cos.AuthorizationTransport{
			SecretID:  config.GlobConfig.OSS.SecretID,
			SecretKey: config.GlobConfig.OSS.SecretKey,
		},
	})

	// 生成预签名URL
	presignedURL, err := client.Object.GetPresignedURL(
		ctx,
		http.MethodPut,                  // 使用PUT方法进行上传
		fileName,                        // 对象键（文件路径）
		config.GlobConfig.OSS.SecretID,  // SecretID
		config.GlobConfig.OSS.SecretKey, // SecretKey
		expiration,                      // 签名有效时长
		nil,                             // 扩展选项，可以为nil
	)
	if err != nil {
		return "", fmt.Errorf("generate presigned URL failed: %w", err)
	}

	return presignedURL.String(), nil
}

// GetPresignedUploadURLWithOptions 生成带选项的预签名上传URL
// 用户可以通过options参数设置特定的请求头部和查询参数
func (s *UploadService) GetPresignedUploadURLWithOptions(ctx context.Context, fileName string, expiration time.Duration, options *cos.PresignedURLOptions) (string, error) {
	// 初始化COS客户端
	u, _ := url.Parse(config.GlobConfig.OSS.BucketURL)
	b := &cos.BaseURL{BucketURL: u}
	client := cos.NewClient(b, &http.Client{
		Transport: &cos.AuthorizationTransport{
			SecretID:  config.GlobConfig.OSS.SecretID,
			SecretKey: config.GlobConfig.OSS.SecretKey,
		},
	})

	// 生成预签名URL
	presignedURL, err := client.Object.GetPresignedURL(
		ctx,
		http.MethodPut,                  // 使用PUT方法进行上传
		fileName,                        // 对象键（文件路径）
		config.GlobConfig.OSS.SecretID,  // SecretID
		config.GlobConfig.OSS.SecretKey, // SecretKey
		expiration,                      // 签名有效时长
		options,                         // 扩展选项，可以包含特定的Header和Query参数
	)
	if err != nil {
		return "", fmt.Errorf("generate presigned URL with options failed: %w", err)
	}

	return presignedURL.String(), nil
}

// GetPresignedDownloadURL 生成预签名下载URL
func (s *UploadService) GetPresignedDownloadURL(ctx context.Context, fileName string, expiration time.Duration) (string, error) {
	// 初始化COS客户端
	u, _ := url.Parse(config.GlobConfig.OSS.BucketURL)
	b := &cos.BaseURL{BucketURL: u}
	client := cos.NewClient(b, &http.Client{
		Transport: &cos.AuthorizationTransport{
			SecretID:  config.GlobConfig.OSS.SecretID,
			SecretKey: config.GlobConfig.OSS.SecretKey,
		},
	})

	// 生成预签名下载URL
	presignedURL, err := client.Object.GetPresignedURL(
		ctx,
		http.MethodGet,                  // 使用GET方法进行下载
		fileName,                        // 对象键（文件路径）
		config.GlobConfig.OSS.SecretID,  // SecretID
		config.GlobConfig.OSS.SecretKey, // SecretKey
		expiration,                      // 签名有效时长
		nil,                             // 扩展选项，可以为nil
	)
	if err != nil {
		return "", fmt.Errorf("generate presigned download URL failed: %w", err)
	}

	domainParsed, err2 := url.Parse(config.GlobConfig.OSS.Domain)
	if err2 == nil && domainParsed.Host != "" {
		scheme := domainParsed.Scheme
		if scheme == "" {
			scheme = presignedURL.Scheme
		}
		newPath := presignedURL.Path
		if domainParsed.Path != "" && domainParsed.Path != "/" {
			newPath = strings.TrimRight(domainParsed.Path, "/") + newPath
		}
		final := fmt.Sprintf("%s://%s%s", scheme, domainParsed.Host, newPath)
		if presignedURL.RawQuery != "" {
			final = final + "?" + presignedURL.RawQuery
		}
		return final, nil
	}

	return presignedURL.String(), nil
}

// CreateFileData 创建文件记录
func (s *UploadService) GetFileUrl(ctx context.Context, md5 string) (string, error) {
	upload := store.QueryDB().MUpload
	uploadCtx := upload.WithContext(ctx)
	uploadInfo, err := uploadCtx.Where(upload.Md5.Eq(md5), upload.IsDeleted.Zero()).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return "", err
	}
	if uploadInfo == nil {
		return "", nil
	}
	return uploadInfo.URL, nil
}

// CreateFileData 创建文件记录
func (s *UploadService) CreateFileData(ctx context.Context, fileName, md5, url, ossURL string, fileSize int64) error {
	// 获取文件扩展名
	fileType := util.GetFileType(fileName)

	upload := store.QueryDB().MUpload
	uploadCtx := upload.WithContext(ctx)
	err := uploadCtx.Create(&model.MUpload{
		FileName:    fileName,
		FileSize:    fileSize,
		FileType:    fileType,
		Md5:         md5,
		URL:         url,
		OssURL:      ossURL,
		OssBucket:   config.GlobConfig.OSS.BucketURL,
		Description: "工单附件",
		CreatedAt:   time.Now().UnixMilli(),
		UpdatedAt:   time.Now().UnixMilli(),
	})
	if err != nil {
		return err
	}
	return nil
}

// CreateFaceFusionFileData 创建人脸融合文件记录
func (s *UploadService) CreateFaceFusionFileData(ctx context.Context, fileName, md5, url, ossURL string, fileSize int64) error {
	// 获取文件扩展名
	fileType := util.GetFileType(fileName)

	upload := store.QueryDB().MUpload
	uploadCtx := upload.WithContext(ctx)
	err := uploadCtx.Create(&model.MUpload{
		FileName:    fileName,
		FileSize:    fileSize,
		FileType:    fileType,
		Md5:         md5,
		URL:         url,
		OssURL:      ossURL,
		OssBucket:   config.GlobConfig.OSS.BucketURL,
		Description: "人脸融合结果图片",
		CreatedAt:   time.Now().UnixMilli(),
		UpdatedAt:   time.Now().UnixMilli(),
	})
	if err != nil {
		return err
	}
	return nil
}
