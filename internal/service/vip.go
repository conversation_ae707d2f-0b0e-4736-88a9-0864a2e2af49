package service

import (
	"context"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"gorm.io/gorm"
)

var (
	_vipServiceOnce sync.Once
	_vipService     *VIPService
)

// VIPUser 简化的VIP用户结构
type VIPUser struct {
	ID                int64     `json:"id"`
	UserID            string    `json:"user_id"`
	GameID            string    `json:"game_id"`
	PlatformID        string    `json:"platform_id"`
	PlatformType      string    `json:"platform_type"`
	CustomerServiceID string    `json:"customer_service_id"`
	IsAuthenticated   bool      `json:"is_authenticated"`
	AssignedAt        int64     `json:"assigned_at"`
	AuthenticatedAt   int64     `json:"authenticated_at"`
	LastPopupDate     time.Time `json:"last_popup_date"`
	CreatedAt         int64     `json:"created_at"`
	UpdatedAt         int64     `json:"updated_at"`
}

// VIPCustomerService 简化的客服信息结构
type VIPCustomerService struct {
	ServiceID       string `json:"service_id"`
	ServiceName     string `json:"service_name"`
	WechatQrCodeURL string `json:"wechat_qr_code_url"`
	IsActive        bool   `json:"is_active"`
}

type VIPService struct{}

func SingletonVIPService() *VIPService {
	_vipServiceOnce.Do(func() {
		_vipService = &VIPService{}
	})
	return _vipService
}

// GetVIPUser 获取VIP用户信息（保留原方法以兼容其他代码）
func (s *VIPService) GetVIPUser(ctx context.Context, userID, gameID string) (*VIPUser, error) {
	vipUser := store.QueryDB().MVipUser

	result, err := vipUser.WithContext(ctx).
		Where(vipUser.UserID.Eq(userID), vipUser.IsDeleted.Is(false)).
		First()
		// vipUser.GameID.Eq(gameID), 暂时不添加game_id(目前有游戏在切换)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		logger.Logger.ErrorfCtx(ctx, "Failed to get VIP user %s in game %s: %v", userID, gameID, err)
		return nil, err
	}

	// 转换为service层的VIPUser结构
	return &VIPUser{
		ID:                int64(result.ID),
		UserID:            result.UserID,
		GameID:            result.GameID,
		PlatformType:      result.PlatformType,
		CustomerServiceID: result.CustomerServiceID,
		IsAuthenticated:   result.IsAuthenticated,
		AssignedAt:        result.AssignedAt,
		AuthenticatedAt:   result.AuthenticatedAt,
		LastPopupDate:     result.LastPopupDate,
		CreatedAt:         result.CreatedAt,
		UpdatedAt:         result.UpdatedAt,
	}, nil
}

// GetVIPUserByUserID 只根据用户ID获取VIP用户信息
func (s *VIPService) GetVIPUserByUserID(ctx context.Context, userID string) (*VIPUser, error) {
	vipUser := store.QueryDB().MVipUser

	result, err := vipUser.WithContext(ctx).
		Where(vipUser.UserID.Eq(userID), vipUser.IsDeleted.Is(false)).
		First()

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		logger.Logger.ErrorfCtx(ctx, "Failed to get VIP user %s: %v", userID, err)
		return nil, err
	}

	// 转换为service层的VIPUser结构
	return &VIPUser{
		ID:                int64(result.ID),
		UserID:            result.UserID,
		GameID:            result.GameID,
		PlatformType:      result.PlatformType,
		CustomerServiceID: result.CustomerServiceID,
		IsAuthenticated:   result.IsAuthenticated,
		AssignedAt:        result.AssignedAt,
		AuthenticatedAt:   result.AuthenticatedAt,
		LastPopupDate:     result.LastPopupDate,
		CreatedAt:         result.CreatedAt,
		UpdatedAt:         result.UpdatedAt,
	}, nil
}

// CheckGameExists 检查游戏是否存在于m_game表中
func (s *VIPService) CheckGameExists(ctx context.Context, gameID string) (bool, error) {
	game := store.QueryDB().MGame

	count, err := game.WithContext(ctx).
		Where(game.GameID.Eq(gameID), game.IsDeleted.Is(false)).
		Count()

	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Failed to check game exists %s: %v", gameID, err)
		return false, err
	}

	return count > 0, nil
}

// GetCustomerService 获取客服信息
func (s *VIPService) GetCustomerService(ctx context.Context, serviceID string) (*VIPCustomerService, error) {
	customerService := store.QueryDB().MVipCustomerServiceBasic

	result, err := customerService.WithContext(ctx).
		Where(customerService.ServiceID.Eq(serviceID), customerService.IsDeleted.Is(false)).
		First()

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		logger.Logger.ErrorfCtx(ctx, "Failed to get customer service %s: %v", serviceID, err)
		return nil, err
	}

	// 转换为service层的VIPCustomerService结构
	return &VIPCustomerService{
		ServiceID:   result.ServiceID,
		ServiceName: result.ServiceName,
		IsActive:    result.IsActive,
	}, nil
}

// HasPopupToday 检查今日是否已弹窗 - 使用VIP用户表的last_popup_date字段（保留原方法以兼容其他代码）
func (s *VIPService) HasPopupToday(ctx context.Context, userID, gameID, date string) (bool, error) {
	vipUser, err := s.GetVIPUser(ctx, userID, gameID)
	if err != nil {
		return false, err
	}

	if vipUser == nil {
		return false, nil
	}

	// 检查last_popup_date是否为今天（排除未设置的早期日期）
	sentinelDate := time.Date(1970, 1, 1, 0, 0, 0, 0, time.UTC)
	if vipUser.LastPopupDate.After(sentinelDate) {
		lastPopupDate := vipUser.LastPopupDate.Format("2006-01-02")
		return lastPopupDate == date, nil
	}

	return false, nil
}

// HasPopupTodayByUserID 只根据用户ID检查今日是否已弹窗
func (s *VIPService) HasPopupTodayByUserID(ctx context.Context, userID, date string) (bool, error) {
	vipUser, err := s.GetVIPUserByUserID(ctx, userID)
	if err != nil {
		return false, err
	}

	if vipUser == nil {
		return false, nil
	}

	// 检查last_popup_date是否为今天（排除未设置的早期日期）
	sentinelDate := time.Date(1970, 1, 1, 0, 0, 0, 0, time.UTC)
	if vipUser.LastPopupDate.After(sentinelDate) {
		lastPopupDate := vipUser.LastPopupDate.Format("2006-01-02")
		return lastPopupDate == date, nil
	}

	return false, nil
}

// HasAnyPopupTodayByUserID 按用户聚合检查：任意記錄在當天已彈窗即返回true
func (s *VIPService) HasAnyPopupTodayByUserID(ctx context.Context, userID, date string) (bool, error) {
	// 將日期字符串解析為當天起止時間區間 [startOfDay, endOfDay)
	parsedDate, err := time.ParseInLocation("2006-01-02", date, time.Local)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Failed to parse date %s: %v", date, err)
		return false, err
	}

	startOfDay := parsedDate
	endOfDay := parsedDate.Add(24 * time.Hour)

	vipUser := store.QueryDB().MVipUser
	count, err := vipUser.WithContext(ctx).
		Where(
			vipUser.UserID.Eq(userID),
			vipUser.IsDeleted.Is(false),
			vipUser.LastPopupDate.Gte(startOfDay),
			vipUser.LastPopupDate.Lt(endOfDay),
		).
		Count()
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Failed to aggregate check popup today for user %s: %v", userID, err)
		return false, err
	}

	return count > 0, nil
}

// RecordPopup 记录弹窗日志 - 更新VIP用户表的last_popup_date字段（保留原方法以兼容其他代码）
func (s *VIPService) RecordPopup(ctx context.Context, userID, gameID, date string) error {
	now := time.Now()

	// 解析date字符串为time.Time（本地时区）
	popupDate, err := time.ParseInLocation("2006-01-02", date, time.Local)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Failed to parse date %s: %v", date, err)
		return err
	}

	// 更新VIP用户的最后弹窗日期
	vipUser := store.QueryDB().MVipUser
	_, err = vipUser.WithContext(ctx).
		Where(vipUser.UserID.Eq(userID), vipUser.GameID.Eq(gameID), vipUser.IsDeleted.Is(false)).
		Updates(map[string]interface{}{
			"last_popup_date": popupDate,
			"updated_at":      now.UnixMilli(),
		})

	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Failed to update last popup date for user %s: %v", userID, err)
		return err
	}

	logger.Logger.InfofCtx(ctx, "Popup recorded for user %s in game %s on %s", userID, gameID, date)
	return nil
}

// RecordPopupByUserID 只根据用户ID记录弹窗日志
func (s *VIPService) RecordPopupByUserID(ctx context.Context, userID, date string) error {
	now := time.Now()

	// 解析date字符串为time.Time（本地时区）
	popupDate, err := time.ParseInLocation("2006-01-02", date, time.Local)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Failed to parse date %s: %v", date, err)
		return err
	}

	// 更新VIP用户的最后弹窗日期（只使用user_id）
	vipUser := store.QueryDB().MVipUser
	_, err = vipUser.WithContext(ctx).
		Where(vipUser.UserID.Eq(userID), vipUser.IsDeleted.Is(false)).
		Updates(map[string]interface{}{
			"last_popup_date": popupDate,
			"updated_at":      now.UnixMilli(),
		})

	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Failed to update last popup date for user %s: %v", userID, err)
		return err
	}

	logger.Logger.InfofCtx(ctx, "Popup recorded for user %s on %s", userID, date)
	return nil
}

// GetUserTotalRechargeAmount 获取用户累计充值金额（返回元为单位）
func (s *VIPService) GetUserTotalRechargeAmount(ctx context.Context, userID, gameID string) (float64, error) {
	// 先从汇总表查询
	rechargeSummary := store.QueryDB().MVipUserRechargeSummary

	result, err := rechargeSummary.WithContext(ctx).
		Where(rechargeSummary.UserID.Eq(userID), rechargeSummary.GameID.Eq(gameID)).
		Select(rechargeSummary.TotalAmount).
		First()

	if err == nil {
		// 将分转换为元
		return float64(result.TotalAmount) / 100.0, nil
	}

	if err != gorm.ErrRecordNotFound {
		logger.Logger.ErrorfCtx(ctx, "Failed to get recharge summary: %v", err)
		return 0, err
	}

	// 汇总表没有数据，从订单表实时计算
	return s.calculateRechargeAmountFromOrders(ctx, userID, gameID)
}

// calculateRechargeAmountFromOrders 从订单表实时计算充值金额
func (s *VIPService) calculateRechargeAmountFromOrders(ctx context.Context, userID, gameID string) (float64, error) {
	order := store.QueryDB().AOrder

	var totalAmount int64
	err := order.WithContext(ctx).
		Where(order.UserID.Eq(userID), order.GameID.Eq(gameID), order.Status.Eq(4), order.IsDeleted.Is(false)).
		Select(order.Money.Sum()).
		Scan(&totalAmount)

	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Failed to calculate recharge amount from orders: %v", err)
		return 0, err
	}

	// 金额从分转换为元
	return float64(totalAmount) / 100, nil
}

// CreateVIPUser 创建VIP用户记录（幂等操作）
func (s *VIPService) CreateVIPUser(ctx context.Context, userID, gameID, platformType string) error {
	// 首先检查用户是否已存在
	existingUser, err := s.GetVIPUser(ctx, userID, gameID)
	if err != nil {
		return err
	}

	if existingUser != nil {
		// 用户已存在，无需重复创建
		logger.Logger.DebugfCtx(ctx, "VIP user already exists: user_id=%s game_id=%s", userID, gameID)
		return nil
	}

	// 创建新的VIP用户记录
	now := time.Now().UnixMilli()

	vipUser := &model.MVipUser{
		UserID:               userID,
		GameID:               gameID,
		PlatformType:         platformType,
		CustomerServiceID:    "10001", // 固定值
		IsAuthenticated:      false,
		AssignedAt:           now,
		AuthenticatedAt:      0,
		LastPopupDate:        time.Date(1900, 1, 1, 0, 0, 0, 0, time.UTC), // 使用有效的早期日期作为"未设置"标识
		AuthenticationRemark: "",
		CreatedAt:            now,
		UpdatedAt:            now,
		IsDeleted:            false,
	}

	vipUserTable := store.QueryDB().MVipUser
	err = vipUserTable.WithContext(ctx).Create(vipUser)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Failed to create VIP user: user_id=%s game_id=%s err=%v", userID, gameID, err)
		return err
	}

	logger.Logger.InfofCtx(ctx, "VIP user created successfully: user_id=%s game_id=%s platform_type=%s", userID, gameID, platformType)
	return nil
}

// GetVIPPopupImageURL 根据游戏ID查询VIP弹窗图片URL
func (s *VIPService) GetVIPPopupImageURL(ctx context.Context, gameID string) (string, error) {
	vipRule := store.QueryDB().MVipAssignmentRule

	result, err := vipRule.WithContext(ctx).
		Where(vipRule.GameID.Eq(gameID)).
		Where(vipRule.IsActive.Is(true)).
		Where(vipRule.PopupEnabled.Is(true)).
		Where(vipRule.IsDeleted.Is(false)).
		First()

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			logger.Logger.DebugfCtx(ctx, "VIP popup rule not found: game_id=%s", gameID)
			return "", nil
		}
		logger.Logger.ErrorfCtx(ctx, "Failed to get VIP popup image URL for game %s: %v", gameID, err)
		return "", err
	}

	return result.PopupImageURL, nil
}

// IsVIPPopupEnabled 根据规则表检查是否开启弹窗
func (s *VIPService) IsVIPPopupEnabled(ctx context.Context, gameID string) (bool, error) {
	vipRule := store.QueryDB().MVipAssignmentRule

	count, err := vipRule.WithContext(ctx).
		Where(vipRule.GameID.Eq(gameID)).
		Where(vipRule.IsActive.Is(true)).
		Where(vipRule.PopupEnabled.Is(true)).
		Where(vipRule.IsDeleted.Is(false)).
		Count()

	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Failed to check VIP popup enabled for game %s: %v", gameID, err)
		return false, err
	}

	return count > 0, nil
}

// HasAnyAuthenticatedVIPByUserID 检查该用户是否存在任意已认证的VIP记录
func (s *VIPService) HasAnyAuthenticatedVIPByUserID(ctx context.Context, userID string) (bool, error) {
	vipUser := store.QueryDB().MVipUser

	count, err := vipUser.WithContext(ctx).
		Where(
			vipUser.UserID.Eq(userID),
			vipUser.IsDeleted.Is(false),
			vipUser.IsAuthenticated.Is(true),
		).
		Count()

	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Failed to check authenticated VIP for user %s: %v", userID, err)
		return false, err
	}

	return count > 0, nil
}
