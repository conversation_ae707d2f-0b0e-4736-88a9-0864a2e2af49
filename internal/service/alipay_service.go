package service

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"strings"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
	"github.com/google/uuid"
	alipay "github.com/smartwalle/alipay/v3"
	"gorm.io/gorm"
)

var (
	_alipayOnce    sync.Once
	_alipayService *AlipayService
)

type AlipayService struct {
	mu             sync.RWMutex
	clientsByGame  map[string]*alipay.Client // gameID -> client
	clientsByAppID map[string]*alipay.Client // appID  -> client（辅助查找）
	defaultClient  *alipay.Client            // 回退默认client（第一条成功加载）
	isProduction   bool                      // 环境标志
}

func SingletonAlipayService() *AlipayService {
	_alipayOnce.Do(func() {
		svc := &AlipayService{clientsByGame: make(map[string]*alipay.Client), clientsByAppID: make(map[string]*alipay.Client)}
		svc.initEnvFlag()
		svc.loadAllConfigs()
		_alipayService = svc
	})
	return _alipayService
}

// initEnvFlag 初始化环境标志
func (s *AlipayService) initEnvFlag() {
	s.isProduction = true
}

// loadAllConfigs 加载所有未删除的支付宝配置，构建多 client 映射
func (s *AlipayService) loadAllConfigs() {
	cfgQ := store.QueryDB().AConfigAlipay
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	configs, err := cfgQ.WithContext(ctx).
		Where(cfgQ.IsDeleted.Is(false)).
		Order(cfgQ.ID.Asc()).
		Find()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Logger.Warnf("alipay configs not found in DB; service remains uninitialized")
		} else {
			logger.Logger.Errorf("query alipay configs failed: %v", err)
		}
		return
	}

	for _, c := range configs {
		if c == nil || c.AppID == "" || c.PrivateKey == "" { // 关键字段校验
			logger.Logger.Warnf("skip invalid alipay config record id=%v: missing app_id/private_key", func() interface{} {
				if c != nil {
					return c.ID
				}
				return 0
			}())
			continue
		}
		client, err := alipay.New(c.AppID, c.PrivateKey, s.isProduction)
		if err != nil {
			logger.Logger.Errorf("init alipay client failed for app_id=%s: %v", c.AppID, err)
			continue
		}
		if c.PublicKey != "" { // 加载公钥
			if err := client.LoadAliPayPublicKey(c.PublicKey); err != nil {
				logger.Logger.Errorf("load alipay public key failed for app_id=%s: %v", c.AppID, err)
			}
		}
		s.mu.Lock()
		if s.defaultClient == nil { // 第一条成功的作为默认
			s.defaultClient = client
		}
		s.clientsByGame[c.GameID] = client
		s.clientsByAppID[c.AppID] = client
		s.mu.Unlock()
	}
}

// GetAppIDByGameID 从数据库读取指定 gameID 的 app_id
func (s *AlipayService) GetAppIDByGameID(ctx context.Context, gameID string) (string, error) {
	cfgQ := store.QueryDB().AConfigAlipay
	cfg, err := cfgQ.WithContext(ctx).
		Select(cfgQ.AppID).
		Where(cfgQ.GameID.Eq(gameID)).
		Where(cfgQ.IsDeleted.Is(false)).
		First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return "", nil
		}
		return "", err
	}
	return cfg.AppID, nil
}

// getClientByGameID 返回对应game的client，若不存在返回nil
func (s *AlipayService) getClientByGameID(gameID string) *alipay.Client {
	s.mu.RLock()
	defer s.mu.RUnlock()
	if c, ok := s.clientsByGame[gameID]; ok {
		return c
	}
	return s.defaultClient
}

// LoginAlipay 使用授权码换token并获取或创建本地用户
// 返回通用用户结构和支付宝用户唯一ID作为openID使用
func (s *AlipayService) LoginAlipay(ctx context.Context, gameID string, authCode string, channel string, adFrom string) (*bean.User, error) {
	logger.Logger.InfofCtx(ctx, "[LoginAlipay] 收到请求, gameID: %s, channel: %s, adFrom: %s", gameID, channel, adFrom)
	if authCode == "" {
		logger.Logger.WarnfCtx(ctx, "[LoginAlipay] 缺少auth_code, gameID: %s", gameID)
		return nil, errors.New("auth_code is required")
	}
	client := s.getClientByGameID(gameID)
	if client == nil {
		logger.Logger.ErrorfCtx(ctx, "[LoginAlipay] 未找到alipay client, gameID: %s", gameID)
		return nil, errors.New("alipay client for game not initialized")
	}

	// 第一阶段：按 auth_code 加短锁，串行化换 token
	codeLockKey := fmt.Sprintf("%s:%s", constants.SystemLoginAlipayLockKey, authCode)
	if !redis.Lock(ctx, codeLockKey, constants.SystemLoginLockExpire*time.Second) {
		logger.Logger.WarnfCtx(ctx, "[LoginAlipay] 分布式锁加锁失败, lockKey: %s", codeLockKey)
		return nil, constants.ErrSystemServiceIsBusy
	}
	defer redis.UnLock(ctx, codeLockKey)

	// 1) 换取token（仅用于获取支付宝用户ID；不持久化auth_code/token）
	tokenReq := alipay.SystemOauthToken{}
	tokenReq.GrantType = "authorization_code"
	tokenReq.Code = authCode
	tokenResp, err := client.SystemOauthToken(ctx, tokenReq)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[LoginAlipay] alipay换取token失败, gameID: %s, authCode: %s, err: %v", gameID, authCode, err)
		return nil, err
	}
	if tokenResp == nil || tokenResp.OpenId == "" {
		logger.Logger.ErrorfCtx(ctx, "[LoginAlipay] alipay换取token返回无效, gameID: %s, authCode: %s", gameID, authCode)
		return nil, fmt.Errorf("invalid token response from alipay")
	}

	alipayOpenID := tokenResp.OpenId
	// 支付宝UnionID在本地表未持久化，如后续需要可在表结构扩展后加入

	// 第二阶段：按 open_id + game_id 加长锁，避免同一用户并发执行（沿用现有Lock/UnLock实现）
	openIDLockKey := fmt.Sprintf("%s:%s:%s", constants.SystemLoginAlipayLockKey, gameID, alipayOpenID)
	if !redis.Lock(ctx, openIDLockKey, time.Duration(constants.SystemLoginOpenIDLockExpire)*time.Second) {
		logger.Logger.WarnfCtx(ctx, "[LoginAlipay] open_id锁加锁失败, lockKey: %s", openIDLockKey)
		return nil, constants.ErrSystemServiceIsBusy
	}
	defer redis.UnLock(ctx, openIDLockKey)

	// 2) 持久化或更新本地用户（GORM Table，登录阶段不更新资料；DB列已改为 open_id）
	aUser := store.QueryDB().AUser
	aUserAlipay := store.QueryDB().AUserAlipay

	var userID string
	isRegister := false

	existingUser, err := aUserAlipay.WithContext(ctx).
		Select(aUserAlipay.UserID).
		Where(aUserAlipay.OpenID.Eq(alipayOpenID), aUserAlipay.IsDeleted.Is(false)).
		First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Logger.ErrorfCtx(ctx, "[LoginAlipay] 查询本地支付宝用户失败, openID: %s, err: %v", alipayOpenID, err)
		return nil, err
	}

	if existingUser == nil {
		userID = util.UUID()
		logger.Logger.InfofCtx(ctx, "[LoginAlipay] 新用户注册, userID: %s, openID: %s", userID, alipayOpenID)
		if err := aUser.WithContext(ctx).Create(&model.AUser{
			GameID:  gameID,
			UserID:  userID,
			Channel: defaultIfEmpty(channel, constants.ThinkingdataDefault),
			AdFrom:  defaultIfEmpty(adFrom, constants.ThinkingdataDefault),
		}); err != nil {
			logger.Logger.ErrorfCtx(ctx, "[LoginAlipay] 创建a_user失败, userID: %s, err: %v", userID, err)
			return nil, err
		}
		if err := aUserAlipay.WithContext(ctx).Create(&model.AUserAlipay{
			UserID: userID,
			OpenID: alipayOpenID,
		}); err != nil {
			logger.Logger.ErrorfCtx(ctx, "[LoginAlipay] 创建a_user_alipay失败, userID: %s, openID: %s, err: %v", userID, alipayOpenID, err)
			return nil, err
		}
		isRegister = true
	} else {
		userID = existingUser.UserID
		logger.Logger.InfofCtx(ctx, "[LoginAlipay] 已存在用户, userID: %s, openID: %s", userID, alipayOpenID)
	}

	u := &bean.User{
		IsRegister: isRegister,
		UserInfo: &bean.UserInfo{
			UserID:     userID,
			RegisterAt: time.Now().UnixMilli(),
		},
		ChannelInfo: &bean.ChannelInfo{Channel: defaultIfEmpty(channel, constants.ThinkingdataDefault), ADFrom: defaultIfEmpty(adFrom, constants.ThinkingdataDefault), OpenID: alipayOpenID},
	}
	logger.Logger.InfofCtx(ctx, "[LoginAlipay] 返回用户结构, userID: %s, openID: %s, isRegister: %v", userID, alipayOpenID, isRegister)
	return u, nil
}

// defaultIfEmpty returns fallback if s is empty
func defaultIfEmpty(s string, fallback string) string {
	if s == "" {
		return fallback
	}
	return s
}

// UpdateAlipayInfo 更新支付宝资料
func (s *AlipayService) UpdateAlipayInfo(ctx context.Context, userID string, req *bean.UpdateAlipayUserInfoReq) error {
	client := s.getClientByGameID(req.GameID) // 需要在请求结构中提供GameID，如不存在则回退默认
	if client == nil {
		return errors.New("alipay client not initialized")
	}

	// 优先尝试将传入字符串视作 auth_code 换取 access_token；失败则回退使用原始 auth_token
	tokenToUse := req.AuthToken
	if req.AuthToken != "" {
		tokenReq := alipay.SystemOauthToken{GrantType: "authorization_code", Code: req.AuthToken}
		if tokenResp, exErr := client.SystemOauthToken(ctx, tokenReq); exErr == nil && tokenResp != nil && tokenResp.AccessToken != "" {
			tokenToUse = tokenResp.AccessToken
		}
	}

	// 通过 Alipay 接口拉取用户资料
	var userInfoReq alipay.UserInfoShare
	userInfoReq.AuthToken = tokenToUse

	userInfoRsp, err := client.UserInfoShare(ctx, userInfoReq)
	if err != nil {
		return err
	}
	if userInfoRsp == nil {
		return fmt.Errorf("invalid alipay user info response")
	}

	// 映射 Alipay 返回字段到本地结构
	// 说明：支付宝返回的头像、昵称字段根据授权范围可能为空
	// 性别 gender：m/f，映射为 1/2，未知为 0
	var genderInt int32
	switch userInfoRsp.Gender {
	case "m", "M":
		genderInt = 1
	case "f", "F":
		genderInt = 2
	default:
		genderInt = 0
	}

	// 更新到 a_user_alipay 表
	u := store.QueryDB().AUserAlipay
	_, err = u.WithContext(ctx).Where(u.UserID.Eq(userID), u.IsDeleted.Zero()).
		UpdateSimple(
			u.NickName.Value(userInfoRsp.NickName),
			u.AvatarURL.Value(userInfoRsp.Avatar),
			u.Gender.Value(genderInt),
			u.City.Value(userInfoRsp.City),
			u.Province.Value(userInfoRsp.Province),
			u.Country.Value(""),
		)
	if err != nil {
		return err
	}
	return nil
}

// GetAlipayUserInfo 获取用于加密的用户信息
func (s *AlipayService) GetAlipayUserInfo(ctx context.Context, userID string) (*bean.UserInfo, error) {
	u := store.QueryDB().AUserAlipay
	info, err := u.WithContext(ctx).Where(u.UserID.Eq(userID)).First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Logger.Errorf(constants.LogUserIDNotFound, userID)
		return nil, nil
	} else if err != nil {
		return nil, err
	}
	return &bean.UserInfo{
		UserID:    info.UserID,
		NickName:  info.NickName,
		AvatarURL: info.AvatarURL,
		Gender:    fmt.Sprintf("%d", info.Gender),
		City:      info.City,
		Province:  info.Province,
		Country:   info.Country,
	}, nil
}

// GetOpenIDByUserID 根据用户ID获取支付宝 open_id
func (s *AlipayService) GetOpenIDByUserID(ctx context.Context, userID string) (string, error) {
	u := store.QueryDB().AUserAlipay
	info, err := u.WithContext(ctx).Where(u.UserID.Eq(userID), u.IsDeleted.Zero()).First()
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return "", nil
	} else if err != nil {
		return "", err
	}
	return info.OpenID, nil
}

// VerifyNotifySign 验证支付宝异步通知签名
// params 应包含支付宝回调的所有表单键值对
func (s *AlipayService) VerifyNotifySign(gameID string, params map[string]string) (bool, error) {
	client := s.getClientByGameID(gameID)
	if client == nil {
		return false, errors.New("alipay client not initialized")
	}
	values := url.Values{}
	for k, v := range params {
		if v != "" {
			values.Set(k, v)
		}
	}
	if err := client.VerifySign(values); err != nil {
		return false, err
	}
	return true, nil
}

// VerifyGameCenterSPISign 验证支付宝游戏中心SPI回调签名
// 基于 smartwalle/alipay/v3 实现SPI签名验证
func (s *AlipayService) VerifyGameCenterSPISign(gameID string, req *bean.AlipayGameCenterSPICallbackReq) (bool, error) {
	client := s.getClientByGameID(gameID)
	if client == nil {
		return false, errors.New("alipay client not initialized")
	}

	// 构建签名数据 - SPI签名包含URL参数和POST body参数
	values := url.Values{}

	// URL参数（除了sign）
	if req.Method != "" {
		values.Set("method", req.Method)
	}
	if req.Charset != "" {
		values.Set("charset", req.Charset)
	}
	if req.Version != "" {
		values.Set("version", req.Version)
	}
	// spi 回调 query 中会包含 merchant_app_id，需参与验签
	if req.MerchantAppID != "" {
		values.Set("merchant_app_id", req.MerchantAppID)
	}
	if req.UtcTimestamp != "" {
		values.Set("utc_timestamp", req.UtcTimestamp)
	}
	if req.SignType != "" {
		values.Set("sign_type", req.SignType)
	}

	// POST Body参数
	if req.UserID != "" {
		values.Set("user_id", req.UserID)
	}
	if req.OpenID != "" {
		values.Set("open_id", req.OpenID)
	}
	if req.CustomID != "" {
		values.Set("custom_id", req.CustomID)
	}
	if req.TradeNo != "" {
		values.Set("trade_no", req.TradeNo)
	}
	if req.CPExtra != "" {
		values.Set("cp_extra", req.CPExtra)
	}

	// 添加签名参数用于验证
	if req.Sign != "" {
		values.Set("sign", req.Sign)
	}

	// 使用alipay客户端验证签名
	if err := client.VerifySign(values); err != nil {
		return false, err
	}
	return true, nil
}

// VerifyGameCenterSPISignFromQuery 使用原始 URL 查询参数进行 SPI 验签
// 注意：传入支付宝回调 query 中的键值对，包括 sign 和 sign_type 参数
func (s *AlipayService) VerifyGameCenterSPISignFromQuery(ctx context.Context, gameID string, query url.Values) (bool, error) {
	client := s.getClientByGameID(gameID)
	if client == nil {
		return false, errors.New("alipay client not initialized")
	}
	values := url.Values{}
	for k, vs := range query {

		// 打印k和vs
		logger.Logger.InfofCtx(ctx, "alipay sign: %s, %s", k, vs)
		for _, v := range vs {
			if v != "" {
				values.Add(k, v)
			}
		}
	}
	if err := client.VerifySign(values); err != nil {
		return false, err
	}
	return true, nil
}

// DecodeNotification wraps smartwalle/alipay Client.DecodeNotification to verify sign and parse notification
// It parses request.Form internally via the caller and leverages SDK's VerifySign
func (s *AlipayService) DecodeNotification(_ context.Context, gameID string, form url.Values) (*alipay.Notification, error) {
	client := s.getClientByGameID(gameID)
	if client == nil {
		return nil, errors.New("alipay client not initialized")
	}
	return client.DecodeNotification(form)
}

// SignBytes 使用对应 gameID 的 Alipay 客户端对给定字节数组进行 RSA2 签名并返回 base64 编码后的签名字符串
func (s *AlipayService) SignBytes(gameID string, data []byte) (string, error) {
	client := s.getClientByGameID(gameID)
	if client == nil {
		return "", errors.New("alipay client not initialized")
	}
	sig, err := client.SignBytes(data)
	if err != nil {
		return "", err
	}
	return base64.StdEncoding.EncodeToString(sig), nil
}

// isAlipayBusinessError 判断是否为业务类错误码（非系统错误）
func (s *AlipayService) isAlipayBusinessError(code string) bool {
	switch code {
	case "40004", // Business Failed (如用户未订阅)
		"40001", // 缺少必选参数
		"40002", // 非法的参数
		"40003", // 业务处理失败
		"40006": // 权限不足
		return true
	}
	return false
}

// formatAlipayErrorMessage 统一格式化支付宝错误消息
func formatAlipayErrorMessage(code, msg, subMsg string) string {
	emsg := fmt.Sprintf("code=%s", code)
	if msg != "" {
		emsg = fmt.Sprintf("%s, msg=%s", emsg, msg)
	}
	if subMsg != "" {
		emsg = fmt.Sprintf("%s, sub_msg=%s", emsg, subMsg)
	}
	return emsg
}

// evaluateAlipayTemplateSendResponse 评估模板消息发送结果
func (s *AlipayService) evaluateAlipayTemplateSendResponse(ctx context.Context, result map[string]interface{}, openID string) error {
	// 解析根级结构（当前返回即为扁平结构）
	code, _ := result["code"].(string)
	msg, _ := result["msg"].(string)
	subMsg, _ := result["sub_msg"].(string)
	if code == "" {
		return fmt.Errorf("invalid alipay subscribe message response: empty code")
	}
	if code != constants.AlipaySuccessCode {
		emsg := formatAlipayErrorMessage(code, msg, subMsg)
		if s.isAlipayBusinessError(code) {
			logger.Logger.InfofCtx(ctx, "[SendSubscribeMessage] 支付宝订阅消息业务状态: %s, openID: %s", emsg, openID)
			return nil
		}
		return fmt.Errorf("alipay subscribe message failed: %s", emsg)
	}
	return nil
}

// SendSubscribeMessage 发送支付宝订阅/模板消息
// 参考文档: https://opendocs.alipay.com/mini-game/0d97tw?pathHash=fe0e5bc4
// 说明：支付宝小游戏订阅消息接口在开放平台以 OpenAPI 形式提供，通常为模板消息发送能力。
// 这里使用通用 Payload 请求方式，结合模板ID、open_id、page 以及 data 进行发送。
func (s *AlipayService) SendSubscribeMessage(ctx context.Context, gameID string, openID string, templateID string, page string, data map[string]interface{}) error {
	client := s.getClientByGameID(gameID)
	if client == nil {
		return errors.New("alipay client not initialized")
	}

	// 具体 API 名称依据支付宝小游戏订阅消息能力，常见为模板消息发送接口。
	// 由于不同账号接入形态存在差异，这里采用通用 Payload，并将模板参数填充到 biz_content。
	// 如果后续确认了固定的 API 名称，可将 method 替换为具体方法名。
	payload := alipay.NewPayload("alipay.open.app.mini.templatemessage.send")
	// 业务参数
	payload.AddBizField("to_open_id", openID) // open_id
	payload.AddBizField("user_template_id", templateID)
	// if page != "" {
	// 	payload.AddBizField("page", page)
	// }
	if data != nil {
		// 注意：此处传入对象，SDK 会在构建 biz_content 时统一 JSON 序列化；
		// 若传 string 会导致二次编码成为 "{\"k\":\"v\"}"，从而引发业务参数解析失败。
		payload.AddBizField("data", data)
	}

	// 使用 map 解析响应，兼容支付宝 API 的灵活响应格式
	var result map[string]interface{}
	if err := client.Request(ctx, payload, &result); err != nil {
		return err
	}
	return s.evaluateAlipayTemplateSendResponse(ctx, result, openID)
}

// GameCenterCoinPay 调用 alipay.user.gamecenter.coin.pay 扣减游戏币
// 文档: https://opendocs.alipay.com/mini-game/493f4520_alipay.user.gamecenter.coin.pay?referPath=07x6o1_d04ee670
func (s *AlipayService) GameCenterCoinPay(ctx context.Context, gameID string, biz map[string]interface{}) (map[string]interface{}, error) {
	client := s.getClientByGameID(gameID)
	if client == nil {
		return nil, errors.New("alipay client not initialized")
	}
	payload := alipay.NewPayload("alipay.user.gamecenter.coin.pay")
	// 写入 biz_content
	for k, v := range biz {
		payload.AddBizField(k, v)
	}
	var result map[string]interface{}
	if err := client.Request(ctx, payload, &result); err != nil {
		return nil, err
	}
	return result, nil
}

// VerifyAlipayContentWithOpenID 调用 alipay.security.risk.content.sync.detect 进行文本内容检测，支持传入 open_id
func (s *AlipayService) VerifyAlipayContentWithOpenID(ctx context.Context, gameID, openID, content string) (*bean.AlipayContentCheckResult, error) {
	// 使用 open_id 进行内容检测
	req := &bean.AlipayContentDetectReq{
		ContentType: "TEXT",
		Channel:     "tinyapp-eco-open",
		Scene:       "content_detect",
		Products:    []string{"TJ_POLITICS_MC", "TJ_PORN_MC", "TJ_ILLEGAL_MC", "TJ_TERRORISM_MC", "TJ_ABUSES_MC"},
		Tenants:     "public_chat",     // 审核内容属于群聊、战队、世界等公共频道消息 private_chat、user_info、game_content、other
		DataList:    []string{content}, // 文本内容
		OpenID:      openID,            // 传入 open_id
	}
	return s.VerifyAlipayContentWithReq(ctx, gameID, req)
}

// SubCheckLabel 表示子检测标签
type SubCheckLabel struct {
	HitStrategy int    `json:"hit_strategy"`
	SubLabel    string `json:"sub_label"`
	Rate        string `json:"rate"`
}

// DetectCheckLabels 表示检测标签信息
type DetectCheckLabels struct {
	Label          string           `json:"label"`
	Rate           string           `json:"rate"`
	SubCheckLabels []*SubCheckLabel `json:"sub_check_labels"`
}

// AlipayContentDetectResponse 表示支付宝内容检测API的响应结构
type AlipayContentDetectResponse struct {
	Code              string             `json:"code"`
	Msg               string             `json:"msg"`
	SubCode           string             `json:"sub_code"`
	SubMsg            string             `json:"sub_msg"`
	RequestID         string             `json:"request_id"`
	ResultCode        string             `json:"result_code"`
	ResultMsg         string             `json:"result_msg"`
	IsMeter           bool               `json:"is_meter"`
	MeterProducts     string             `json:"meter_products"`
	IsSync            bool               `json:"is_sync"`
	Suggestion        string             `json:"suggestion"`
	DetectCheckLabels *DetectCheckLabels `json:"detect_check_labels"`
	Action            string             `json:"action"`
	Keywords          []string           `json:"keywords"`
	RiskLevel         string             `json:"risk_level"`
	Result            *struct {
		Action    string   `json:"action"`
		RiskLevel string   `json:"risk_level"`
		Keywords  []string `json:"keywords"`
	} `json:"result"`
}

// AlipayContentDetectRootResponse 表示完整的API响应结构
type AlipayContentDetectRootResponse struct {
	AlipayContentDetectResponse *AlipayContentDetectResponse `json:"alipay_security_risk_content_sync_detect_response"`
	Sign                        string                       `json:"sign"`
}

// parseAlipayContentDetectResp 解析支付宝 content detect 的响应
// 使用JSON重新序列化的方式进行映射，更简洁可靠
func parseAlipayContentDetectResp(res map[string]interface{}) *AlipayContentDetectResponse {
	// 优先取带有完整响应体的键
	var body map[string]interface{}
	if v, ok := res["alipay_security_risk_content_sync_detect_response"]; ok {
		if b, ok2 := v.(map[string]interface{}); ok2 {
			body = b
		}
	}
	if body == nil {
		// 兼容可能的扁平结构
		body = res
	}

	// 将map重新序列化为JSON，然后反序列化到结构体
	jsonData, err := json.Marshal(body)
	if err != nil {
		return nil
	}

	var out AlipayContentDetectResponse
	if err := json.Unmarshal(jsonData, &out); err != nil {
		return nil
	}

	// 最低校验
	if out.Code == "" && out.Action == "" && out.RiskLevel == "" {
		return nil
	}

	return &out
}

// VerifyAlipayContentWithReq 支持与官方示例一致的灵活参数
// - 支持 request_id/tenants/products/channel/content_type/open_id/user_id/data_list/app_auth_token
// - data_list 与 content 二选一；若均提供则优先 data_list
func (s *AlipayService) VerifyAlipayContentWithReq(ctx context.Context, gameID string, req *bean.AlipayContentDetectReq) (*bean.AlipayContentCheckResult, error) {
	client := s.getClientByGameID(gameID)
	if client == nil {
		logger.Logger.ErrorfCtx(ctx, "[AlipayContentCheck] client not found, gameID: %s", gameID)
		return nil, errors.New("alipay client not initialized")
	}

	payload := alipay.NewPayload("alipay.security.risk.content.sync.detect")
	payload.AddBizField("request_id", uuid.New().String())

	// 基本字段
	if len(req.Products) > 0 {
		payload.AddBizField("products", strings.Join(req.Products, ","))
	}
	if req.Channel != "" {
		payload.AddBizField("channel", req.Channel)
	}
	if req.ContentType != "" {
		payload.AddBizField("content_type", req.ContentType)
	}
	if req.OpenID != "" {
		payload.AddBizField("open_id", req.OpenID)
	}
	if req.UserID != "" {
		payload.AddBizField("user_id", req.UserID)
	}
	// 兼容老逻辑中传入的场景字段
	if req.Scene != "" {
		payload.AddBizField("scene", req.Scene)
	}
	if req.Tenants != "" {
		payload.AddBizField("tenants", req.Tenants)
	}
	// 内容字段：优先 data_list
	if len(req.DataList) > 0 {
		payload.AddBizField("data_list", req.DataList)
	}

	var result map[string]interface{}
	if err := client.Request(ctx, payload, &result); err != nil {
		logger.Logger.ErrorfCtx(ctx, "[AlipayContentCheck] API call failed, gameID: %s, error: %v", gameID, err)
		return nil, err
	}

	parsed := parseAlipayContentDetectResp(result)
	if parsed == nil {
		logger.Logger.ErrorfCtx(ctx, "[AlipayContentCheck] parse response failed, gameID: %s, raw: %+v", gameID, result)
		return nil, fmt.Errorf("invalid alipay content detect response")
	}

	// 支付宝成功 code 通常为 AlipaySuccessCode
	if parsed.Code != constants.AlipaySuccessCode {
		// 保留 sub_msg 以便诊断
		msg := parsed.Msg
		if parsed.SubMsg != "" {
			msg = fmt.Sprintf("%s | %s", msg, parsed.SubMsg)
		}
		logger.Logger.WarnfCtx(ctx, "[AlipayContentCheck] non-success code, gameID: %s, code: %s, msg: %s", gameID, parsed.Code, msg)
		return nil, fmt.Errorf("alipay detect failed: code=%s msg=%s", parsed.Code, msg)
	}

	// 统计日志
	contentLen := 0
	if len(req.DataList) > 0 {
		// 近似：按字符串总长度
		contentLen = len(strings.Join(req.DataList, ","))
	}
	logger.Logger.InfofCtx(ctx, "[AlipayContentCheck] Success - gameID: %s, contentLength: %d, action: %s", gameID, contentLen, parsed.Action)
	var detect *bean.AlipayDetectCheckLabels
	if parsed.DetectCheckLabels != nil {
		detect = &bean.AlipayDetectCheckLabels{
			Label: parsed.DetectCheckLabels.Label,
			Rate:  parsed.DetectCheckLabels.Rate,
		}
		if len(parsed.DetectCheckLabels.SubCheckLabels) > 0 {
			subs := make([]*bean.AlipaySubCheckLabel, 0, len(parsed.DetectCheckLabels.SubCheckLabels))
			for _, sc := range parsed.DetectCheckLabels.SubCheckLabels {
				if sc == nil {
					continue
				}
				subs = append(subs, &bean.AlipaySubCheckLabel{HitStrategy: sc.HitStrategy, SubLabel: sc.SubLabel, Rate: sc.Rate})
			}
			detect.SubCheckLabels = subs
		}
	}
	return &bean.AlipayContentCheckResult{
		Suggestion:        parsed.Suggestion,
		Keywords:          parsed.Keywords,
		RiskLevel:         parsed.RiskLevel,
		DetectCheckLabels: detect,
		RequestID:         parsed.RequestID, // 添加RequestID，用于设置TraceID
	}, nil
}

// CheckGameCenterHomepage 查询支付宝小游戏中心用户是否设置了首页
// 调用支付宝开放平台API: alipay.user.gamecenter.addhomepage.consult
func (s *AlipayService) CheckGameCenterHomepage(ctx context.Context, gameID string, openID string) (bool, error) {
	client := s.getClientByGameID(gameID)
	if client == nil {
		logger.Logger.WarnfCtx(ctx, "[CheckGameCenterHomepage] client not found, gameID: %s", gameID)
		return false, errors.New("alipay client not initialized")
	}

	// 创建支付宝API请求
	payload := alipay.NewPayload("alipay.user.gamecenter.addhomepage.consult")
	payload.AddBizField("open_id", openID)

	var result map[string]interface{}
	if err := client.Request(ctx, payload, &result); err != nil {
		logger.Logger.WarnfCtx(ctx, "[CheckGameCenterHomepage] API call failed, gameID: %s, userID: %s, error: %v", gameID, openID, err)
		return false, err
	}

	// 解析响应
	parsed := s.parseGameCenterHomepageResp(result)
	if parsed == nil {
		logger.Logger.WarnfCtx(ctx, "[CheckGameCenterHomepage] parse response failed, gameID: %s, userID: %s, raw: %+v", gameID, openID, result)
		return false, fmt.Errorf("invalid alipay game center homepage response")
	}

	// 检查API调用是否成功
	if parsed.Code != constants.AlipaySuccessCode {
		msg := parsed.Msg
		if parsed.SubMsg != "" {
			msg = fmt.Sprintf("%s | %s", msg, parsed.SubMsg)
		}
		logger.Logger.WarnfCtx(ctx, "[CheckGameCenterHomepage] non-success code, gameID: %s, userID: %s, code: %s, msg: %s", gameID, openID, parsed.Code, msg)
		return false, fmt.Errorf("alipay game center homepage check failed: code=%s msg=%s", parsed.Code, msg)
	}

	// 解析consult_result字段：Y表示已设置首页，N表示未设置
	hasHomepage := parsed.ConsultResult == "Y"
	logger.Logger.InfofCtx(ctx, "[CheckGameCenterHomepage] Success - gameID: %s, userID: %s, consultResult: %s, hasHomepage: %t", gameID, openID, parsed.ConsultResult, hasHomepage)
	return hasHomepage, nil
}

// AlipayGameCenterHomepageResponse 支付宝游戏中心首页查询API响应结构
type AlipayGameCenterHomepageResponse struct {
	Code          string `json:"code"`
	Msg           string `json:"msg"`
	SubCode       string `json:"sub_code"`
	SubMsg        string `json:"sub_msg"`
	ConsultResult string `json:"consult_result"` // Y表示用户已设置首页，N表示未设置
}

// AlipayGameCenterHomepageRootResponse 完整的API响应结构（已废弃，直接解析响应内容）
type AlipayGameCenterHomepageRootResponse struct {
	AlipayGameCenterHomepageResponse *AlipayGameCenterHomepageResponse `json:"alipay_user_gamecenter_addhomepage_consult_response"`
	Sign                             string                            `json:"sign"`
}

// parseGameCenterHomepageResp 解析支付宝游戏中心首页查询的响应
// 根据支付宝API文档，直接解析响应内容，不处理外部的alipay_user_gamecenter_addhomepage_consult_response包装
func (s *AlipayService) parseGameCenterHomepageResp(res map[string]interface{}) *AlipayGameCenterHomepageResponse {
	// 直接解析响应内容
	jsonBytes, err := json.Marshal(res)
	if err != nil {
		return nil
	}

	var parsed AlipayGameCenterHomepageResponse
	if err := json.Unmarshal(jsonBytes, &parsed); err != nil {
		return nil
	}

	return &parsed
}
