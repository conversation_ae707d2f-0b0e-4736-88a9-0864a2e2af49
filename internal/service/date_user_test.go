package service

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestDateService_IsPlayableForUser_AdultUser(t *testing.T) {
	initTestEnvForDate()
	service := SingletonDateService()
	ctx := context.Background()

	beijingLocation, err := time.LoadLocation("Asia/Shanghai")
	assert.NoError(t, err)

	// 测试成年用户（18岁以上）在任何时间都可以玩
	adultAge := 20

	// 测试周一凌晨2点（非可玩时间段，非周末）
	mondayMorning := time.Date(2024, 1, 8, 2, 0, 0, 0, beijingLocation) // 2024年1月8日是周一
	isPlayable, err := service.IsPlayableForUser(ctx, adultAge, mondayMorning)
	assert.NoError(t, err)
	assert.True(t, isPlayable, "成年用户应该在任何时间都可以游玩")

	// 测试周三下午3点
	wednesdayAfternoon := time.Date(2024, 1, 10, 15, 0, 0, 0, beijingLocation) // 2024年1月10日是周三
	isPlayable, err = service.IsPlayableForUser(ctx, adultAge, wednesdayAfternoon)
	assert.NoError(t, err)
	assert.True(t, isPlayable, "成年用户应该在任何时间都可以游玩")
}

func TestDateService_IsPlayableForUser_MinorUser_TimeRestriction(t *testing.T) {
	initTestEnvForDate()
	service := SingletonDateService()
	ctx := context.Background()

	beijingLocation, err := time.LoadLocation("Asia/Shanghai")
	assert.NoError(t, err)

	// 测试未成年用户的时间段限制
	minorAge := 15

	// 测试周五20:30（在可玩时间段内）
	fridayEvening := time.Date(2024, 1, 5, 20, 30, 0, 0, beijingLocation) // 2024年1月5日是周五
	isPlayable, err := service.IsPlayableForUser(ctx, minorAge, fridayEvening)
	assert.NoError(t, err)
	assert.True(t, isPlayable, "未成年用户在周五20:00-21:00时间段应该可以游玩")

	// 测试周五19:30（不在可玩时间段内）
	fridayEarly := time.Date(2024, 1, 5, 19, 30, 0, 0, beijingLocation)
	isPlayable, err = service.IsPlayableForUser(ctx, minorAge, fridayEarly)
	assert.NoError(t, err)
	assert.False(t, isPlayable, "未成年用户在20:00之前不能游玩")

	// 测试周五21:30（不在可玩时间段内）
	fridayLate := time.Date(2024, 1, 5, 21, 30, 0, 0, beijingLocation)
	isPlayable, err = service.IsPlayableForUser(ctx, minorAge, fridayLate)
	assert.NoError(t, err)
	assert.False(t, isPlayable, "未成年用户在21:00之后不能游玩")
}

func TestDateService_IsPlayableForUser_MinorUser_WeekendRule(t *testing.T) {
	initTestEnvForDate()
	service := SingletonDateService()
	ctx := context.Background()

	beijingLocation, err := time.LoadLocation("Asia/Shanghai")
	assert.NoError(t, err)

	// 测试未成年用户的周末规则
	minorAge := 16

	// 测试周六20:30（周末+可玩时间段）
	saturdayEvening := time.Date(2024, 1, 6, 20, 30, 0, 0, beijingLocation) // 2024年1月6日是周六
	isPlayable, err := service.IsPlayableForUser(ctx, minorAge, saturdayEvening)
	assert.NoError(t, err)
	assert.True(t, isPlayable, "未成年用户在周六20:00-21:00时间段应该可以游玩")

	// 测试周日20:30（周末+可玩时间段）
	sundayEvening := time.Date(2024, 1, 7, 20, 30, 0, 0, beijingLocation) // 2024年1月7日是周日
	isPlayable, err = service.IsPlayableForUser(ctx, minorAge, sundayEvening)
	assert.NoError(t, err)
	assert.True(t, isPlayable, "未成年用户在周日20:00-21:00时间段应该可以游玩")

	// 测试周一20:30（非周末+可玩时间段）
	mondayEvening := time.Date(2024, 1, 8, 20, 30, 0, 0, beijingLocation) // 2024年1月8日是周一
	isPlayable, err = service.IsPlayableForUser(ctx, minorAge, mondayEvening)
	assert.NoError(t, err)
	assert.False(t, isPlayable, "未成年用户在非周末的可玩时间段内也不能游玩（除非有特殊配置）")
}

func TestDateService_IsPlayableForUser_EdgeCases(t *testing.T) {
	initTestEnvForDate()
	service := SingletonDateService()
	ctx := context.Background()

	beijingLocation, err := time.LoadLocation("Asia/Shanghai")
	assert.NoError(t, err)

	// 测试边界年龄
	// 17岁（未成年）
	minorAge := 17
	fridayEvening := time.Date(2024, 1, 5, 20, 30, 0, 0, beijingLocation)
	isPlayable, err := service.IsPlayableForUser(ctx, minorAge, fridayEvening)
	assert.NoError(t, err)
	assert.True(t, isPlayable, "17岁未成年用户在周五可玩时间段应该可以游玩")

	// 18岁（成年）
	adultAge := 18
	mondayMorning := time.Date(2024, 1, 8, 2, 0, 0, 0, beijingLocation)
	isPlayable, err = service.IsPlayableForUser(ctx, adultAge, mondayMorning)
	assert.NoError(t, err)
	assert.True(t, isPlayable, "18岁成年用户应该在任何时间都可以游玩")

	// 测试边界时间
	// 20:00整点
	exactTwentyOClock := time.Date(2024, 1, 5, 20, 0, 0, 0, beijingLocation)
	isPlayable, err = service.IsPlayableForUser(ctx, 15, exactTwentyOClock)
	assert.NoError(t, err)
	assert.True(t, isPlayable, "未成年用户在20:00整点应该可以游玩")

	// 20:59:59
	almostTwentyOne := time.Date(2024, 1, 5, 20, 59, 59, 0, beijingLocation)
	isPlayable, err = service.IsPlayableForUser(ctx, 15, almostTwentyOne)
	assert.NoError(t, err)
	assert.True(t, isPlayable, "未成年用户在20:59:59应该可以游玩")

	// 21:00整点
	exactTwentyOneOClock := time.Date(2024, 1, 5, 21, 0, 0, 0, beijingLocation)
	isPlayable, err = service.IsPlayableForUser(ctx, 15, exactTwentyOneOClock)
	assert.NoError(t, err)
	assert.False(t, isPlayable, "未成年用户在21:00整点应该不可以游玩")
}
