package service

import (
	"context"
	"testing"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

// TestCreateVIPUser_Idempotency 测试VIP用户创建的幂等性
func TestCreateVIPUser_Idempotency(t *testing.T) {
	// 注意：此测试需要数据库连接，实际运行时需要配置测试数据库

	ctx := context.Background()
	service := SingletonVIPService()

	userID := "test_user_123"
	gameID := "test_game_456"
	platformType := "wechat"

	// 第一次创建VIP用户
	err1 := service.CreateVIPUser(ctx, userID, gameID, platformType)
	if err1 != nil {
		t.Logf("First creation result: %v", err1)
	}

	// 第二次创建相同VIP用户（应该是幂等的）
	err2 := service.CreateVIPUser(ctx, userID, gameID, platformType)
	if err2 != nil {
		t.Logf("Second creation result: %v", err2)
	}

	// 验证用户存在
	vipUser, err := service.GetVIPUser(ctx, userID, gameID)
	if err != nil {
		t.Logf("Get VIP user error: %v", err)
		return
	}

	if vipUser != nil {
		t.Logf("VIP user found - ID: %d, UserID: %s, GameID: %s, Platform: %s",
			vipUser.ID, vipUser.UserID, vipUser.GameID, vipUser.PlatformType)

		// 验证默认值
		if vipUser.CustomerServiceID != "10001" {
			t.Errorf("Expected CustomerServiceID to be '10001', got '%s'", vipUser.CustomerServiceID)
		}

		if vipUser.IsAuthenticated {
			t.Errorf("Expected IsAuthenticated to be false, got true")
		}
	} else {
		t.Log("VIP user not found")
	}
}

// MockVIPUser 用于测试的模拟VIP用户创建
func MockVIPUser(userID, gameID, platformType string) *model.MVipUser {
	now := time.Now().UnixMilli()

	return &model.MVipUser{
		UserID:               userID,
		GameID:               gameID,
		PlatformType:         platformType,
		CustomerServiceID:    "10001",
		IsAuthenticated:      false,
		AssignedAt:           now,
		AuthenticatedAt:      0,
		LastPopupDate:        time.Date(1900, 1, 1, 0, 0, 0, 0, time.UTC),
		AuthenticationRemark: "",
		CreatedAt:            now,
		UpdatedAt:            now,
		IsDeleted:            false,
	}
}
