package service

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/go-resty/resty/v2"
)

var (
	wechatMessageOnce    sync.Once
	wechatMessageService *WechatMessageService
)

type WechatMessageService struct {
	client          *resty.Client
	minigameService *MinigameService
	userService     *UserService
}

func SingletonWechatMessageService() *WechatMessageService {
	wechatMessageOnce.Do(func() {
		wechatMessageService = &WechatMessageService{
			client:          resty.New(),
			minigameService: SingletonMinigameService(),
			userService:     SingletonUserService(),
		}
	})

	return wechatMessageService
}

const (
	subMessageUrl = "/cgi-bin/message/subscribe/send" // 次数限制：开通支付能力的是3kw/日，没开通的是1kw/日。
)

// MessageSendResult 消息发送结果
type MessageSendResult struct {
	Success   bool   // 是否发送成功
	ErrCode   int32  // 微信API错误码
	ErrMsg    string // 错误信息
	IsRefused bool   // 是否为用户拒绝接收消息（43101或43108）
}

// FallbackResult 回退发送结果（包含回退信息）
type FallbackResult struct {
	*MessageSendResult
	HasFallback bool   // 是否进行了回退发送
	FallbackMsg string // 回退相关信息
}

// ToLegacyString 将结构化结果转换为传统的字符串格式（用于向后兼容）
func (fr *FallbackResult) ToLegacyString() string {
	if fr.Success {
		if fr.HasFallback {
			return "" // 成功（通过回退）- 为了兼容性仍返回空字符串，但日志中会有回退信息
		}
		return "" // 成功（首次发送）
	}

	// 失败情况，返回错误信息
	return fmt.Sprintf("code: %d, msg: %s", fr.ErrCode, fr.ErrMsg)
}

// ToDetailedString 将结构化结果转换为详细的字符串格式（包含回退信息）
func (fr *FallbackResult) ToDetailedString() string {
	if fr.Success {
		if fr.HasFallback {
			return fmt.Sprintf("success_with_fallback: %s", fr.FallbackMsg)
		}
		return "success"
	}

	if fr.HasFallback {
		return fmt.Sprintf("failed_after_fallback: code: %d, msg: %s, fallback: %s", fr.ErrCode, fr.ErrMsg, fr.FallbackMsg)
	}
	return fmt.Sprintf("code: %d, msg: %s", fr.ErrCode, fr.ErrMsg)
}

// NewSuccessResult 创建成功结果
func NewSuccessResult() *MessageSendResult {
	return &MessageSendResult{
		Success:   true,
		ErrCode:   0,
		ErrMsg:    "",
		IsRefused: false,
	}
}

// NewErrorResult 创建错误结果
func NewErrorResult(errCode int32, errMsg string) *MessageSendResult {
	return &MessageSendResult{
		Success:   false,
		ErrCode:   errCode,
		ErrMsg:    errMsg,
		IsRefused: errCode == 43101 || errCode == 43108,
	}
}

// SubMessageNotify 发送微信消息订阅
func (s *WechatMessageService) SubMessageNotify(ctx context.Context, accessToken string, openID string, req *bean.SubMessageNotifyReq) (string, error) {
	logger.Logger.InfofCtx(ctx, "SubMessageNotify openID: %s", openID)
	logger.Logger.InfofCtx(ctx, "SubMessageNotify accessToken: %s", accessToken)
	logger.Logger.InfofCtx(ctx, "SubMessageNotify req: %+v", req)

	body := map[string]interface{}{
		"touser":      openID,         // OpenID
		"template_id": req.TemplateID, // 订阅消息模板ID
		"page":        req.Page,       // 点击订阅消息卡片后会打开的小程序页面
		"data":        req.Data,
	}

	resp, err := s.client.
		R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetBody(body).
		Post(config.GlobConfig.Minigame.BaseURL + subMessageUrl + fmt.Sprintf("?access_token=%s", accessToken))
	if err != nil {
		return "", err
	}

	if resp.StatusCode() != http.StatusOK {
		logger.Logger.Errorf("SubMessageNotify https status code: %d", resp.StatusCode())
		return "", fmt.Errorf("SubMessageNotify https status code: %d", resp.StatusCode())
	}

	var result bean.MinigameErr
	err = json.Unmarshal(resp.Body(), &result)
	if err != nil {
		logger.Logger.Errorf("SubMessageNotify unmarshal err: %s", err.Error())
		return "", err
	}

	if result.ErrCode != 0 {
		if result.ErrCode == 43101 || result.ErrCode == 43108 { // user refuse to accept the msg
			logger.Logger.Warnf("SubMessageNotify call success, but err, code: %d, msg: %s", result.ErrCode, result.ErrMsg)
		} else {
			logger.Logger.Errorf("SubMessageNotify call success, but err, code: %d, msg: %s", result.ErrCode, result.ErrMsg)
		}
		return fmt.Sprintf("code: %d, msg: %s", result.ErrCode, result.ErrMsg), nil
	}
	return "", nil
}

// SubMessageNotifyStructured 发送微信消息订阅（返回结构化结果）
func (s *WechatMessageService) SubMessageNotifyStructured(ctx context.Context, accessToken string, openID string, req *bean.SubMessageNotifyReq) (*MessageSendResult, error) {
	logger.Logger.InfofCtx(ctx, "SubMessageNotifyStructured openID: %s", openID)
	logger.Logger.InfofCtx(ctx, "SubMessageNotifyStructured accessToken: %s", accessToken)
	logger.Logger.InfofCtx(ctx, "SubMessageNotifyStructured req: %+v", req)

	body := map[string]interface{}{
		"touser":      openID,         // OpenID
		"template_id": req.TemplateID, // 订阅消息模板ID
		"page":        req.Page,       // 点击订阅消息卡片后会打开的小程序页面
		"data":        req.Data,
	}

	resp, err := s.client.
		R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetBody(body).
		Post(config.GlobConfig.Minigame.BaseURL + subMessageUrl + fmt.Sprintf("?access_token=%s", accessToken))
	if err != nil {
		return nil, err
	}

	if resp.StatusCode() != http.StatusOK {
		logger.Logger.ErrorfCtx(ctx, "SubMessageNotifyStructured https status code: %d", resp.StatusCode())
		return nil, fmt.Errorf("SubMessageNotifyStructured https status code: %d", resp.StatusCode())
	}

	var result bean.MinigameErr
	err = json.Unmarshal(resp.Body(), &result)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "SubMessageNotifyStructured unmarshal err: %s", err.Error())
		return nil, err
	}

	if result.ErrCode != 0 {
		if result.ErrCode == 43101 || result.ErrCode == 43108 || result.ErrCode == 40037 { // user refuse to accept the msg
			logger.Logger.WarnfCtx(ctx, "SubMessageNotifyStructured call success, but user refused, code: %d, msg: %s", result.ErrCode, result.ErrMsg)
		} else {
			logger.Logger.ErrorfCtx(ctx, "SubMessageNotifyStructured call success, but err, code: %d, msg: %s", result.ErrCode, result.ErrMsg)
		}
		return NewErrorResult(result.ErrCode, result.ErrMsg), nil
	}

	logger.Logger.InfofCtx(ctx, "SubMessageNotifyStructured success")
	return NewSuccessResult(), nil
}

// SubMessageNotifyWithFallback 带回退机制的发送微信消息订阅
// 当映射用户发送失败时，尝试使用原始用户凭据重新发送
func (s *WechatMessageService) SubMessageNotifyWithFallback(ctx context.Context, accessToken string, openID string, req *bean.SubMessageNotifyReq, originalUserID string, originalGameID string, isMapped bool) (string, error) {
	// 内部使用结构化方法，提供更一致和丰富的逻辑
	structuredResult, err := s.SubMessageNotifyWithFallbackStructured(ctx, accessToken, openID, req, originalUserID, originalGameID, isMapped)
	if err != nil {
		return "", err
	}

	// 转换为传统字符串格式以保持向后兼容
	if structuredResult.Success {
		return "", nil // 成功时返回空字符串（保持兼容性）
	} else {
		return fmt.Sprintf("code: %d, msg: %s", structuredResult.ErrCode, structuredResult.ErrMsg), nil
	}
}

// SubMessageNotifyWithFallbackStructured 带回退机制的发送微信消息订阅（结构化版本）
// 当映射用户发送失败时，尝试使用原始用户凭据重新发送
func (s *WechatMessageService) SubMessageNotifyWithFallbackStructured(ctx context.Context, accessToken string, openID string, req *bean.SubMessageNotifyReq, originalUserID string, originalGameID string, isMapped bool) (*MessageSendResult, error) {
	// 首次发送
	result, err := s.SubMessageNotifyStructured(ctx, accessToken, openID, req)
	if err != nil {
		return nil, err
	}

	// 检查是否需要回退（用户拒绝消息且进行了用户映射）
	if s.shouldFallbackStructured(result, originalUserID, originalGameID, isMapped) {
		logger.Logger.InfofCtx(ctx, "[微信消息回退] 检测到用户拒绝消息，开始回退发送, 错误码: %d, 错误信息: %s, 原始用户ID: %s, 原始游戏ID: %s",
			result.ErrCode, result.ErrMsg, originalUserID, originalGameID)
		return s.fallbackSendStructured(ctx, req, originalUserID, originalGameID)
	}

	return result, nil
}

// shouldFallbackStructured 判断是否需要回退发送（结构化版本）
func (s *WechatMessageService) shouldFallbackStructured(result *MessageSendResult, originalUserID string, originalGameID string, isMapped bool) bool {
	// 1. 检查是否进行了用户映射
	if !isMapped {
		return false
	}

	// 2. 检查是否有原始用户信息
	if originalUserID == "" || originalGameID == "" {
		return false
	}

	// 3. 直接检查是否为用户拒绝错误
	return result != nil && result.IsRefused
}

// shouldFallback 判断是否需要回退发送（保留用于兼容性，但推荐使用结构化版本）
// 已弃用：建议使用 shouldFallbackStructured
func (s *WechatMessageService) shouldFallback(result string, originalUserID string, originalGameID string, isMapped bool) bool {
	// 为了保持向后兼容而保留，内部转换为结构化判断
	errCode := s.parseErrorCodeFromResult(result)
	mockResult := &MessageSendResult{
		Success:   errCode == 0,
		ErrCode:   errCode,
		IsRefused: errCode == 43101 || errCode == 43108,
	}
	return s.shouldFallbackStructured(mockResult, originalUserID, originalGameID, isMapped)
}

// parseErrorCodeFromResult 从格式化的错误结果字符串中解析错误码
// 已弃用：建议使用结构化API
func (s *WechatMessageService) parseErrorCodeFromResult(result string) int32 {
	if result == "" {
		return 0 // 空字符串表示成功
	}

	// 使用正则表达式解析 "code: 数字" 格式
	re := regexp.MustCompile(`code:\s*(\d+)`)
	matches := re.FindStringSubmatch(result)
	if len(matches) >= 2 {
		if code, err := strconv.ParseInt(matches[1], 10, 32); err == nil {
			return int32(code)
		}
	}

	// 如果正则解析失败，作为fallback仍然使用字符串匹配（保持兼容性）
	if strings.Contains(result, "code: 43101") {
		return 43101
	}
	if strings.Contains(result, "code: 43108") {
		return 43108
	}

	// 无法解析，返回-1表示未知错误
	return -1
}

// fallbackSend 执行回退发送（保留用于兼容性，但推荐使用结构化版本）
// 已弃用：建议使用 fallbackSendStructured
func (s *WechatMessageService) fallbackSend(ctx context.Context, req *bean.SubMessageNotifyReq, originalUserID string, originalGameID string) (string, error) {
	// 内部使用结构化方法
	result, err := s.fallbackSendStructured(ctx, req, originalUserID, originalGameID)
	if err != nil {
		return "", err
	}

	// 转换为字符串格式
	if result.Success {
		return "", nil
	} else {
		return fmt.Sprintf("code: %d, msg: %s", result.ErrCode, result.ErrMsg), nil
	}
}

// fallbackSendStructured 执行回退发送（结构化版本）
func (s *WechatMessageService) fallbackSendStructured(ctx context.Context, req *bean.SubMessageNotifyReq, originalUserID string, originalGameID string) (*MessageSendResult, error) {
	// 1. 获取原始游戏配置
	originalConfig, err := s.minigameService.GetMinigameConfig(ctx, originalGameID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[微信消息回退] 获取原始游戏配置失败: %v, 游戏ID: %s", err, originalGameID)
		return nil, err
	}

	// 2. 获取原始用户信息
	originalUser, err := s.userService.GetMinigameModel(ctx, originalUserID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[微信消息回退] 获取原始用户信息失败: %v, 用户ID: %s", err, originalUserID)
		return nil, err
	}

	// 3. 使用原始凭据重新发送
	logger.Logger.InfofCtx(ctx, "[微信消息回退] 使用原始凭据重新发送, 原始用户ID: %s, 原始游戏ID: %s, OpenID: %s",
		originalUserID, originalGameID, originalUser.OpenID)

	result, err := s.SubMessageNotifyStructured(ctx, originalConfig.AccessToken, originalUser.OpenID, req)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "[微信消息回退] 回退发送失败: %v", err)
		return nil, err
	}

	// 4. 检查回退发送结果
	if result.IsRefused {
		logger.Logger.WarnfCtx(ctx, "[微信消息回退] 回退发送仍然失败，用户拒绝接收消息, 错误码: %d, 错误信息: %s", result.ErrCode, result.ErrMsg)
	} else if result.Success {
		logger.Logger.InfofCtx(ctx, "[微信消息回退] 回退发送成功")
	} else {
		logger.Logger.WarnfCtx(ctx, "[微信消息回退] 回退发送失败, 错误码: %d, 错误信息: %s", result.ErrCode, result.ErrMsg)
	}

	return result, nil
}

func (s *WechatMessageService) SubMessageNotifySetReturnEnv(ctx context.Context, accessToken, openID, returnEnv string, req *bean.SubMessageNotifyReq) (string, error) {
	body := map[string]interface{}{
		"touser":            openID,         // OpenID
		"template_id":       req.TemplateID, // 订阅消息模板ID
		"miniprogram_state": returnEnv,
		"page":              req.Page, // 点击订阅消息卡片后会打开的小程序页面
		"data":              req.Data,
	}

	logger.Logger.Infof("SubMessageNotifySetReturnEnv body: %v", body)

	resp, err := s.client.
		R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetBody(body).
		Post(config.GlobConfig.Minigame.BaseURL + subMessageUrl + fmt.Sprintf("?access_token=%s", accessToken))
	if err != nil {
		return "", err
	}

	if resp.StatusCode() != http.StatusOK {
		logger.Logger.Errorf("SubMessageNotify https status code: %d", resp.StatusCode())
		return "", fmt.Errorf("SubMessageNotify https status code: %d", resp.StatusCode())
	}

	var result bean.MinigameErr
	err = json.Unmarshal(resp.Body(), &result)
	if err != nil {
		logger.Logger.Errorf("SubMessageNotify unmarshal err: %s", err.Error())
		return "", err
	}

	if result.ErrCode != 0 {
		if result.ErrCode == 43101 || result.ErrCode == 43108 { // user refuse to accept the msg
			logger.Logger.Warnf("SubMessageNotify call success, but err, code: %d, msg: %s", result.ErrCode, result.ErrMsg)
		} else {
			logger.Logger.Errorf("SubMessageNotify call success, but err, code: %d, msg: %s", result.ErrCode, result.ErrMsg)
		}
		return fmt.Sprintf("code: %d, msg: %s", result.ErrCode, result.ErrMsg), nil
	}
	return "", nil
}
