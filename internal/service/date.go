package service

import (
	"context"
	"errors"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"gorm.io/gorm"
)

var (
	_dateOnce    sync.Once
	_dateService *DateService
)

type DateService struct{}

func SingletonDateService() *DateService {
	_dateOnce.Do(func() {
		_dateService = &DateService{}
	})
	return _dateService
}

// IsPlayableForUser 根据用户年龄和当前时间判断是否可玩
// 逻辑：
//  1. 成年用户（≥18岁）：任何时间都可以玩游戏
//  2. 未成年用户（<18岁）：
//     a. 首先检查当前时间是否在可玩时间段（20:00-21:00）内，如果不在，返回 false
//     b. 检查数据库中今天的配置：
//     - 如果有记录且 is_playable = 1（可玩），返回 true
//     - 如果有记录且 is_playable = 0（不可玩），返回 false
//     - 如果没有记录，检查是否是周五六日，是的话返回 true，否则返回 false
func (s *DateService) IsPlayableForUser(ctx context.Context, userAge int, currentTime time.Time) (bool, error) {
	// 转换为北京时区
	beijingLocation, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "DateService IsPlayableForUser: 加载北京时区失败, err: %v", err)
		return false, err
	}

	beijingTime := currentTime.In(beijingLocation)

	logger.Logger.InfofCtx(ctx, "DateService IsPlayableForUser: 检查用户是否可玩, 年龄: %d, 当前时间: %s",
		userAge, beijingTime.Format("2006-01-02 15:04:05"))

	// 1. 成年用户（≥18岁）：任何时间都可以玩游戏
	if userAge >= 18 {
		logger.Logger.InfofCtx(ctx, "DateService IsPlayableForUser: 用户为成年人（%d岁），任何时间都可以游玩", userAge)
		return true, nil
	}

	// 2. 未成年用户（<18岁）：需要检查时间段和日期配置
	logger.Logger.InfofCtx(ctx, "DateService IsPlayableForUser: 用户为未成年人（%d岁），需要检查时间段和日期配置", userAge)

	// 2a. 检查当前时间是否在可玩时间段（20:00-21:00）内
	// hour := beijingTime.Hour()
	// if hour < 20 || hour >= 21 {
	// 	logger.Logger.InfofCtx(ctx, "DateService IsPlayableForUser: 当前时间 %d:00 不在未成年人可玩时间段（20:00-21:00）内", hour)
	// 	return false, nil
	// }

	// 2b. 检查数据库中今天的配置
	dateStr := beijingTime.Format("2006-01-02")

	h5PlayableDate := store.QueryDB().H5PlayableDate
	record, err := h5PlayableDate.WithContext(ctx).
		Where(h5PlayableDate.PlayableDate.Eq(dateStr)).
		Where(h5PlayableDate.IsDeleted.Is(false)).
		First()

	if err != nil {
		// 区分不同类型的错误
		if errors.Is(err, context.Canceled) {
			logger.Logger.InfofCtx(ctx, "DateService IsPlayableForUser: 查询可玩日期被客户端取消, date: %s", dateStr)
			return false, err
		} else if errors.Is(err, context.DeadlineExceeded) {
			logger.Logger.WarnfCtx(ctx, "DateService IsPlayableForUser: 查询可玩日期超时, date: %s", dateStr)
			return false, err
		} else if errors.Is(err, gorm.ErrRecordNotFound) {
			// 数据库中没有找到记录，检查是否是周五六日
			logger.Logger.InfofCtx(ctx, "DateService IsPlayableForUser: 数据库中没有找到日期记录, date: %s, 检查是否为周末", dateStr)
		} else {
			// 其他数据库错误
			logger.Logger.ErrorfCtx(ctx, "DateService IsPlayableForUser: 查询数据库失败, date: %s, err: %v", dateStr, err)
			return false, err
		}
	}

	// 如果找到了记录，根据 is_playable 字段判断
	if record != nil {
		isPlayable := record.IsPlayable == 1
		logger.Logger.InfofCtx(ctx, "DateService IsPlayableForUser: 在数据库中找到日期配置, date: %s, is_playable: %d, 结果: %v",
			dateStr, record.IsPlayable, isPlayable)
		return isPlayable, nil
	}

	// 如果没有记录，检查是否是周五六日（默认可玩时间）
	weekday := beijingTime.Weekday()
	isWeekend := weekday == time.Friday || weekday == time.Saturday || weekday == time.Sunday

	if isWeekend {
		logger.Logger.InfofCtx(ctx, "DateService IsPlayableForUser: 日期 %s 是周末（%s），未成年人在20:00-21:00时间段可以游玩",
			dateStr, weekday.String())
		return true, nil
	}

	logger.Logger.InfofCtx(ctx, "DateService IsPlayableForUser: 日期 %s 不是可玩日期（非周末且数据库中无特殊配置）",
		dateStr)
	return false, nil
}

// IsPlayableDate 判断指定日期是否为可玩日期（兼容性方法，建议使用 IsPlayableForUser）
// 逻辑：
// 1. 首先查询数据库表中是否存在当天日期的记录
// 2. 如果查询到匹配的日期记录，根据 is_playable 字段判断（1=可玩，0=不可玩）
// 3. 如果数据库中没有找到当天日期的记录，则检查当天是否为周五、周六或周日
// 4. 如果当天是周五、周六或周日，返回 true
// 5. 如果既没有在数据库中找到记录，当天也不是周五、周六、周日，则返回 false
func (s *DateService) IsPlayableDate(ctx context.Context, date time.Time) (bool, error) {
	// 将时间转换为北京时区的日期（只保留年月日）
	beijingLocation, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "DateService IsPlayableDate: 加载北京时区失败, err: %v", err)
		return false, err
	}

	// 转换为北京时区并只保留日期部分
	beijingDate := date.In(beijingLocation)
	dateStr := beijingDate.Format("2006-01-02")

	logger.Logger.InfofCtx(ctx, "DateService IsPlayableDate: 检查日期 %s 是否为可玩日期", dateStr)

	// 1. 首先查询数据库表中是否存在当天日期的记录
	h5PlayableDate := store.QueryDB().H5PlayableDate
	record, err := h5PlayableDate.WithContext(ctx).
		Where(h5PlayableDate.PlayableDate.Eq(dateStr)).
		Where(h5PlayableDate.IsDeleted.Is(false)).
		First()

	if err != nil {
		// 区分context相关错误和真正的系统错误
		if errors.Is(err, context.Canceled) {
			logger.Logger.InfofCtx(ctx, "DateService IsPlayableDate: 查询可玩日期被客户端取消, date: %s", dateStr)
			return false, err
		} else if errors.Is(err, context.DeadlineExceeded) {
			logger.Logger.WarnfCtx(ctx, "DateService IsPlayableDate: 查询可玩日期超时, date: %s", dateStr)
			return false, err
		} else if errors.Is(err, gorm.ErrRecordNotFound) {
			// 数据库中没有找到记录，检查是否是周五六日
			logger.Logger.InfofCtx(ctx, "DateService IsPlayableDate: 数据库中没有找到日期记录, date: %s, 检查是否为周末", dateStr)
		} else {
			// 其他数据库错误
			logger.Logger.ErrorfCtx(ctx, "DateService IsPlayableDate: 查询数据库失败, date: %s, err: %v", dateStr, err)
			return false, err
		}
	}

	// 2. 如果找到了记录，根据 is_playable 字段判断
	if record != nil {
		isPlayable := record.IsPlayable == 1
		logger.Logger.InfofCtx(ctx, "DateService IsPlayableDate: 在数据库中找到日期配置, date: %s, is_playable: %d, 结果: %v",
			dateStr, record.IsPlayable, isPlayable)
		return isPlayable, nil
	}

	// 3. 如果数据库中没有找到当天日期的记录，则检查当天是否为周五、周六或周日
	weekday := beijingDate.Weekday()
	isWeekend := weekday == time.Friday || weekday == time.Saturday || weekday == time.Sunday

	if isWeekend {
		logger.Logger.InfofCtx(ctx, "DateService IsPlayableDate: 日期 %s 是周末（%s），判定为可玩日期",
			dateStr, weekday.String())
		return true, nil
	}

	// 4. 既没有在数据库中找到记录，当天也不是周五、周六、周日，则返回 false
	logger.Logger.InfofCtx(ctx, "DateService IsPlayableDate: 日期 %s 不是可玩日期（非周末且不在数据库记录中）",
		dateStr)
	return false, nil
}

// IsTodayPlayable 判断今天是否为可玩日期（兼容性方法，默认按未成年人规则处理）
// 建议使用 IsPlayableForUser 方法以获得更准确的结果
func (s *DateService) IsTodayPlayable(ctx context.Context) (bool, error) {
	// 默认按未成年人规则处理，使用年龄17岁作为默认值
	return s.IsPlayableForUser(ctx, 17, time.Now())
}
