package service

import (
	"context"
	"testing"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
)

// TestGetMappedUserID 测试用户ID映射功能
func TestGetMappedUserID(t *testing.T) {
	ctx := context.Background()
	userService := &UserService{}

	tests := []struct {
		name     string
		unionID  string
		gameID   string
		expected string
		hasError bool
	}{
		{
			name:     "功能禁用时应返回空字符串",
			unionID:  "test-union-id",
			gameID:   config.GlobConfig.UserIDMapping.NewGameID,
			expected: "",
			hasError: false,
		},
		{
			name:     "非目标游戏ID应返回空字符串",
			unionID:  "test-union-id",
			gameID:   "other-game",
			expected: "",
			hasError: false,
		},
		{
			name:     "空UnionID应返回空字符串",
			unionID:  "",
			gameID:   config.GlobConfig.UserIDMapping.NewGameID,
			expected: "",
			hasError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := userService.GetMappedUserID(ctx, tt.unionID, tt.gameID)

			if tt.hasError && err == nil {
				t.Errorf("期望有错误，但没有错误")
			}
			if !tt.hasError && err != nil {
				t.Errorf("不期望有错误，但有错误: %v", err)
			}
			if result != tt.expected {
				t.Errorf("期望结果 %s，但得到 %s", tt.expected, result)
			}
		})
	}
}

// TestUserIDMappingConstants 测试配置常量
func TestUserIDMappingConstants(t *testing.T) {
	// 验证配置常量是否正确设置
	if config.GlobConfig.UserIDMapping.NewGameID != "kof" {
		t.Errorf("期望新游戏ID为 'kof'，但得到 '%s'", config.GlobConfig.UserIDMapping.NewGameID)
	}

	if config.GlobConfig.UserIDMapping.OldGameID != "hlxq" {
		t.Errorf("期望旧游戏ID为 'hlxq'，但得到 '%s'", config.GlobConfig.UserIDMapping.OldGameID)
	}

	if !config.GlobConfig.UserIDMapping.Enabled {
		t.Errorf("期望映射功能启用，但当前为禁用状态")
	}
}
