package service

import (
	"bytes"
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/sha1"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"sort"
	"strings"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/forgoer/openssl"
)

var (
	_secretOnce    sync.Once
	_secretService *SecretService
)

type SecretService struct{}

func SingletonSecretService() *SecretService {
	_secretOnce.Do(func() {
		_secretService = &SecretService{}
	})
	return _secretService
}

// Encrypt 加密 （参考疯狂游戏）
func (s *SecretService) Encrypt(_ context.Context, secret string, originValue interface{}) (string, error) {
	secretBytes := []byte(secret)
	jsonData, err := json.Marshal(originValue)
	if err != nil {
		logger.Logger.Errorf("Service Encrypt: json.Marshal err: %v", err)
		return "", err
	}
	// 加密成功后返回
	result, err := aesEncrypt(jsonData, secretBytes)
	if err != nil {
		logger.Logger.Errorf("Service Encrypt: err: %s", err.Error())
		return "", err
	}
	return base64.StdEncoding.EncodeToString(result), nil
}

// WechatDataDecrypt 解密微信小游戏
func (s *SecretService) WechatDataDecrypt(_ context.Context, sessionKey, encryptedData, iv string) (*bean.WechatUserInfo, error) {
	aesKey, err := base64.StdEncoding.DecodeString(sessionKey)
	if err != nil {
		return nil, err
	}

	aesCipherText, err := base64.StdEncoding.DecodeString(encryptedData)
	if err != nil {
		return nil, err
	}

	aesIV, err := base64.StdEncoding.DecodeString(iv)
	if err != nil {
		return nil, err
	}

	block, err := aes.NewCipher(aesKey)
	if err != nil {
		logger.Logger.Errorf("Service WechatDataDecrypt: aes.NewCipher fail: %")
		return nil, err
	}

	mode := cipher.NewCBCDecrypter(block, aesIV)
	mode.CryptBlocks(aesCipherText, aesCipherText)

	// PKCS#7反填充
	aesCipherText = pkcs7UnPadding(aesCipherText)
	var userInfo *bean.WechatUserInfo
	err = json.Unmarshal(aesCipherText, &userInfo)
	if err != nil {
		logger.Logger.Errorf("Service WechatDataDecrypt: json unmarshal fail, val: %s", string(aesCipherText))
		logger.Logger.Errorf("Service WechatDataDecrypt: json unmarshal fail, err: %s", err.Error())
		return nil, err
	}
	return userInfo, nil
}

func (s *SecretService) WechatDataDecryptStr(sessionKey, encryptedData, iv string) (string, error) {
	aesKey, err := base64.StdEncoding.DecodeString(sessionKey)
	if err != nil {
		return "", err
	}

	aesCipherText, err := base64.StdEncoding.DecodeString(encryptedData)
	if err != nil {
		return "", err
	}

	aesIV, err := base64.StdEncoding.DecodeString(iv)
	if err != nil {
		return "", err
	}

	block, err := aes.NewCipher(aesKey)
	if err != nil {
		logger.Logger.Errorf("WechatDataDecryptKvList: aes.NewCipher fail: %")
		return "", err
	}

	mode := cipher.NewCBCDecrypter(block, aesIV)
	mode.CryptBlocks(aesCipherText, aesCipherText)

	aesCipherText = pkcs7UnPadding(aesCipherText)
	return string(aesCipherText), nil
}

func (s *SecretService) DouyinDecrypt(sessionKey, encryptedData, iv string) (*bean.DouyinUserInfo, error) {
	aesKey, err := base64.StdEncoding.DecodeString(sessionKey)
	if err != nil {
		return nil, err
	}

	aesCipherText, err := base64.StdEncoding.DecodeString(encryptedData)
	if err != nil {
		return nil, err
	}

	aesIV, err := base64.StdEncoding.DecodeString(iv)
	if err != nil {
		return nil, err
	}

	block, err := aes.NewCipher(aesKey)
	if err != nil {
		logger.Logger.Errorf("Service WechatDataDecrypt: aes.NewCipher fail: %")
		return nil, err
	}

	mode := cipher.NewCBCDecrypter(block, aesIV)
	mode.CryptBlocks(aesCipherText, aesCipherText)

	// PKCS#7反填充
	aesCipherText = pkcs7UnPadding(aesCipherText)
	var userInfo *bean.DouyinUserInfo
	err = json.Unmarshal(aesCipherText, &userInfo)
	if err != nil {
		logger.Logger.Errorf("Service WechatDataDecrypt: json unmarshal fail, err: %s", err.Error())
		return nil, err
	}
	return userInfo, nil
}

// Sign 生成签名
func (s *SecretService) Sign(_ context.Context, secret string, timeStamp int64, encryptCombUser string) (string, error) {
	info := []string{
		fmt.Sprintf("encrypt_user=%s", encryptCombUser),
		fmt.Sprintf("secret=%s", secret),
		fmt.Sprintf("timestamp=%d", timeStamp),
	}
	// 升序排序
	sort.Strings(info)
	combined := strings.Join(info, "")
	logger.Logger.Infof("SecretService Sign: %s", combined)
	return util.EncodeMD5(combined), nil
}

func pkcs7UnPadding(origData []byte) []byte {
	length := len(origData)
	unPadding := int(origData[length-1])
	return origData[:(length - unPadding)]
}

func pkcs7Padding(ciphertext []byte, blockSize int) []byte {
	padding := blockSize - len(ciphertext)%blockSize
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padText...)
}

func aesEncrypt(origData, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	blockSize := block.BlockSize()
	origData = pkcs7Padding(origData, blockSize)
	blockMode := cipher.NewCBCEncrypter(block, key[:blockSize])
	crypto := make([]byte, len(origData))
	blockMode.CryptBlocks(crypto, origData)
	return crypto, nil
}

// SortRawData
func (s *SecretService) SortRawData(rawData string) string {
	var data map[string]interface{}
	err := json.Unmarshal([]byte(rawData), &data)
	if err != nil {
		return ""
	}

	sortedJSON, err := json.Marshal(data) // 自1.12起，默认按键的字典顺序进行升序排序
	if err != nil {
		return ""
	}

	return string(sortedJSON)
}

// VerifySignature 校验微信抖音用户信息一致性
func (s *SecretService) VerifySignature(rawData, signature, sessionKey string) error {
	// 校验抖音用户信息一致性
	if newSign := s.Signature(sessionKey, rawData); newSign != signature {
		return fmt.Errorf("VerifySignature signature not match， newSign: %s, signature:%s", newSign, signature)
	}
	return nil
}

// VerifySignature 校验微信抖音用户信息一致性
func (s *SecretService) VerifyDouyinSignature(rawData, signature, sessionKey string) error {
	// 校验抖音用户信息一致性
	if newSign := s.Signature(sessionKey, rawData); newSign != signature {
		logger.Logger.Warnf("VerifyDouyinSignature signature not match， newSign: %s, signature:%s", newSign, signature)
		return constants.ErrDouyinSignatureFailed
	}
	return nil
}

// Signature 签名
func (s *SecretService) Signature(sessionKey, rawData string) string {
	signatureInput := rawData + sessionKey
	h := sha1.New()
	h.Write([]byte(signatureInput))
	bs := h.Sum(nil)

	return hex.EncodeToString(bs)
}

func (s *SecretService) DecryptData(key, code, iv string) (string, error) {
	// Convert UTF-8 key and iv directly (no base64 decoding needed)
	keyBytes := []byte(key)
	ivBytes := []byte(iv)

	// Decode base64 ciphertext
	ciphertext, err := base64.StdEncoding.DecodeString(code)
	if err != nil {
		return "", fmt.Errorf("decode ciphertext error: %v", err)
	}

	// Create cipher using openssl package
	dst, err := openssl.AesCBCDecrypt(ciphertext, keyBytes, ivBytes, openssl.PKCS7_PADDING)
	if err != nil {
		return "", fmt.Errorf("decrypt error: %v", err)
	}

	return string(dst), nil
}
