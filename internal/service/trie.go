package service

import (
	"fmt"
	"hash/fnv"
	"sort"
	"strings"
	"sync"
	"unicode"
)

var (
	_trieOnce    sync.Once
	_trieService *TrieService
)

type TrieNode struct {
	Children map[rune]*TrieNode
	IsEnd    bool
}

type TrieService struct {
	trieNode  *TrieNode
	gameTries sync.Map // key: gameID:ignoreCase -> *gameTrieEntry
}

type gameTrieEntry struct {
	root      *TrieNode
	signature string
	mu        sync.RWMutex
}

// EnsureGameTriePrecomputed 基于预计算的词表与签名构建/复用Trie
func (s *TrieService) EnsureGameTriePrecomputed(gameID string, ignoreCase int32, words []string, signature string) {
	key := fmt.Sprintf("%s:%d", gameID, ignoreCase)

	entryIface, _ := s.gameTries.LoadOrStore(key, &gameTrieEntry{})
	entry := entryIface.(*gameTrieEntry)

	entry.mu.RLock()
	if entry.root != nil && entry.signature == signature {
		entry.mu.RUnlock()
		return
	}
	entry.mu.RUnlock()

	entry.mu.Lock()
	defer entry.mu.Unlock()
	if entry.root != nil && entry.signature == signature {
		return
	}
	newRoot := &TrieNode{Children: make(map[rune]*TrieNode)}
	for _, w := range words {
		addWordToRoot(newRoot, w)
	}
	entry.root = newRoot
	entry.signature = signature
}

func SingletonTrieService() *TrieService {
	_trieOnce.Do(func() {
		_trieService = &TrieService{
			trieNode: &TrieNode{Children: make(map[rune]*TrieNode)},
		}
	})
	return _trieService
}

func (s *TrieService) AddWord(word string) {
	curr := s.trieNode
	for _, char := range word {
		if _, ok := curr.Children[char]; !ok {
			curr.Children[char] = &TrieNode{Children: make(map[rune]*TrieNode)}
		}
		curr = curr.Children[char]
	}
	curr.IsEnd = true
}

// Clear 清空字典树，重置为初始状态
func (s *TrieService) Clear() {
	s.trieNode = &TrieNode{Children: make(map[rune]*TrieNode)}
}

func (s *TrieService) CheckAndReplace(sentence string) string {
	var builder strings.Builder
	runes := []rune(sentence)
	i := 0
	for i < len(runes) {
		match, length := s.matchFromIndex(runes, i)
		if match {
			builder.WriteString(strings.Repeat("*", length))
			i += length // Skip over the matched phrase
		} else {
			builder.WriteRune(runes[i])
			i++ // Proceed normally
		}
	}
	return builder.String()
}

func (s *TrieService) CheckAndReplaceByCase(sentence string, ignoreCase int32) string {
	var builder strings.Builder
	runes := []rune(sentence)
	i := 0
	for i < len(runes) {
		match, length := s.matchFromIndexByCase(runes, i, ignoreCase)
		if match {
			builder.WriteString(strings.Repeat("*", length))
			i += length // Skip over the matched phrase
		} else {
			builder.WriteRune(runes[i])
			i++ // Proceed normally
		}
	}
	return builder.String()
}

func (s *TrieService) matchFromIndex(runes []rune, index int) (bool, int) {
	curr := s.trieNode
	match := false
	length := 0
	for i := index; i < len(runes) && curr != nil; i++ {
		next, exists := curr.Children[runes[i]]
		if !exists {
			break
		}
		length++
		curr = next
		if curr.IsEnd {
			match = true
			break
		}
	}
	return match && length > 0, length
}

func (s *TrieService) matchFromIndexByCase(runes []rune, index int, ignoreCase int32) (bool, int) {
	curr := s.trieNode
	match := false
	length := 0
	for i := index; i < len(runes) && curr != nil; i++ {
		r := runes[i]
		// 如果忽略大小写，则将字符转换为小写
		if ignoreCase == 1 {
			r = unicode.ToLower(r)
		}

		next, exists := curr.Children[r]
		if !exists {
			break
		}
		length++
		curr = next
		if curr.IsEnd {
			match = true
			break
		}
	}
	return match && length > 0, length
}

func (s *TrieService) Check(sentence string) []string {
	var results []string
	runes := []rune(sentence)
	i := 0
	for i < len(runes) {
		match, word := s.matchFromWord(runes, i)
		if match {
			results = append(results, word)
			i += len([]rune(word))
		} else {
			i++
		}
	}
	return results
}

func (s *TrieService) CheckByCase(sentence string, ignoreCase int32) []string {
	var results []string
	runes := []rune(sentence)
	i := 0
	for i < len(runes) {
		match, word := s.matchFromWordByCase(runes, i, ignoreCase)
		if match {
			results = append(results, word)
			i += len([]rune(word))
		} else {
			i++
		}
	}
	return results
}

func (s *TrieService) matchFromWord(runes []rune, index int) (bool, string) {
	curr := s.trieNode
	matchWord := ""
	for i := index; i < len(runes) && curr != nil; i++ {
		next, exists := curr.Children[runes[i]]
		if !exists {
			break
		}
		curr = next
		matchWord += string(runes[i])
		if curr.IsEnd {
			break
		}
	}
	if len(matchWord) > 0 && curr.IsEnd {
		return true, matchWord
	}
	return false, ""
}

func (s *TrieService) matchFromWordByCase(runes []rune, index int, ignoreCase int32) (bool, string) {
	curr := s.trieNode
	matchWord := ""
	for i := index; i < len(runes) && curr != nil; i++ {
		r := runes[i]
		// 如果忽略大小写，则将字符转换为小写
		if ignoreCase == 1 {
			r = unicode.ToLower(r)
		}
		next, exists := curr.Children[r]
		if !exists {
			break
		}
		curr = next
		matchWord += string(runes[i]) // 保持原始大小写
		if curr.IsEnd {
			break
		}
	}
	if len(matchWord) > 0 && curr.IsEnd {
		return true, matchWord
	}
	return false, ""
}

// EnsureGameTrie 确保按游戏与忽略大小写配置构建的Trie缓存存在且最新
func (s *TrieService) EnsureGameTrie(gameID string, ignoreCase int32, words []string) {
	key := fmt.Sprintf("%s:%d", gameID, ignoreCase)
	normalized := normalizeWordsForCase(words, ignoreCase)
	signature := computeWordsSignature(normalized, ignoreCase)

	entryIface, _ := s.gameTries.LoadOrStore(key, &gameTrieEntry{})
	entry := entryIface.(*gameTrieEntry)

	// 先读锁快速判断是否需要重建
	entry.mu.RLock()
	if entry.root != nil && entry.signature == signature {
		entry.mu.RUnlock()
		return
	}
	entry.mu.RUnlock()

	// 写锁下双重检查并重建
	entry.mu.Lock()
	defer entry.mu.Unlock()
	if entry.root != nil && entry.signature == signature {
		return
	}
	newRoot := &TrieNode{Children: make(map[rune]*TrieNode)}
	for _, w := range normalized {
		addWordToRoot(newRoot, w)
	}
	entry.root = newRoot
	entry.signature = signature
}

// CheckByCaseForGame 使用按游戏缓存的Trie进行匹配，返回命中的原文片段
func (s *TrieService) CheckByCaseForGame(sentence string, gameID string, ignoreCase int32) []string {
	key := fmt.Sprintf("%s:%d", gameID, ignoreCase)
	v, ok := s.gameTries.Load(key)
	if !ok {
		return nil
	}
	entry := v.(*gameTrieEntry)
	entry.mu.RLock()
	root := entry.root
	entry.mu.RUnlock()
	if root == nil {
		return nil
	}

	var results []string
	runes := []rune(sentence)
	i := 0
	for i < len(runes) {
		match, word := matchFromWordByCaseWithRoot(runes, i, ignoreCase, root)
		if match {
			results = append(results, word)
			i += len([]rune(word))
		} else {
			i++
		}
	}
	return results
}

// CheckAndReplaceByCaseForGame 使用按游戏缓存的Trie进行替换
func (s *TrieService) CheckAndReplaceByCaseForGame(sentence string, gameID string, ignoreCase int32) string {
	key := fmt.Sprintf("%s:%d", gameID, ignoreCase)
	v, ok := s.gameTries.Load(key)
	if !ok {
		return sentence
	}
	entry := v.(*gameTrieEntry)
	entry.mu.RLock()
	root := entry.root
	entry.mu.RUnlock()
	if root == nil {
		return sentence
	}

	var builder strings.Builder
	runes := []rune(sentence)
	i := 0
	for i < len(runes) {
		match, length := matchFromIndexByCaseWithRoot(runes, i, ignoreCase, root)
		if match {
			builder.WriteString(strings.Repeat("*", length))
			i += length
		} else {
			builder.WriteRune(runes[i])
			i++
		}
	}
	return builder.String()
}

// 辅助：向指定根节点插入词
func addWordToRoot(root *TrieNode, word string) {
	curr := root
	for _, char := range word {
		if _, ok := curr.Children[char]; !ok {
			curr.Children[char] = &TrieNode{Children: make(map[rune]*TrieNode)}
		}
		curr = curr.Children[char]
	}
	curr.IsEnd = true
}

// 辅助：根据忽略大小写配置标准化词表（复制一份，避免外部修改）
func normalizeWordsForCase(words []string, ignoreCase int32) []string {
	if ignoreCase != 1 {
		out := make([]string, len(words))
		copy(out, words)
		return out
	}
	out := make([]string, len(words))
	for i, w := range words {
		out[i] = strings.ToLower(w)
	}
	return out
}

// 辅助：计算稳定的词表签名，避免重复重建
func computeWordsSignature(words []string, ignoreCase int32) string {
	cpy := make([]string, len(words))
	copy(cpy, words)
	sort.Strings(cpy)
	h := fnv.New64a()
	if ignoreCase == 1 {
		_, _ = h.Write([]byte{1})
	} else {
		_, _ = h.Write([]byte{0})
	}
	for _, w := range cpy {
		_, _ = h.Write([]byte{0})
		_, _ = h.Write([]byte(w))
	}
	return fmt.Sprintf("%x", h.Sum64())
}

// 使用指定根节点的匹配函数
func matchFromIndexByCaseWithRoot(runes []rune, index int, ignoreCase int32, root *TrieNode) (bool, int) {
	curr := root
	match := false
	length := 0
	for i := index; i < len(runes) && curr != nil; i++ {
		r := runes[i]
		if ignoreCase == 1 {
			r = unicode.ToLower(r)
		}
		next, exists := curr.Children[r]
		if !exists {
			break
		}
		length++
		curr = next
		if curr.IsEnd {
			match = true
			break
		}
	}
	return match && length > 0, length
}

func matchFromWordByCaseWithRoot(runes []rune, index int, ignoreCase int32, root *TrieNode) (bool, string) {
	curr := root
	matchWord := ""
	for i := index; i < len(runes) && curr != nil; i++ {
		r := runes[i]
		if ignoreCase == 1 {
			r = unicode.ToLower(r)
		}
		next, exists := curr.Children[r]
		if !exists {
			break
		}
		curr = next
		matchWord += string(runes[i])
		if curr.IsEnd {
			break
		}
	}
	if len(matchWord) > 0 && curr.IsEnd {
		return true, matchWord
	}
	return false, ""
}
