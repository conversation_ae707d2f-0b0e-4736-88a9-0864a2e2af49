package service

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestMessageSendResult(t *testing.T) {
	tests := []struct {
		name     string
		errCode  int32
		errMsg   string
		expected bool // expected IsRefused value
	}{
		{
			name:     "成功情况",
			errCode:  0,
			errMsg:   "ok",
			expected: false,
		},
		{
			name:     "用户拒绝消息-43101",
			errCode:  43101,
			errMsg:   "用户拒绝接受消息",
			expected: true,
		},
		{
			name:     "用户拒绝消息-43108",
			errCode:  43108,
			errMsg:   "用户拒绝接受消息",
			expected: true,
		},
		{
			name:     "其他错误",
			errCode:  40003,
			errMsg:   "touser字段openid为空或者不正确",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.errCode == 0 {
				result := NewSuccessResult()
				assert.True(t, result.Success, "期望 Success=true")
				assert.False(t, result.IsRefused, "期望 IsRefused=false")
				assert.Equal(t, int32(0), result.ErrCode, "期望 ErrCode=0")
			} else {
				result := NewErrorResult(tt.errCode, tt.errMsg)
				assert.False(t, result.Success, "期望 Success=false")
				assert.Equal(t, tt.expected, result.IsRefused, "IsRefused 判断错误")
				assert.Equal(t, tt.errCode, result.ErrCode, "ErrCode 不匹配")
				assert.Equal(t, tt.errMsg, result.ErrMsg, "ErrMsg 不匹配")
			}
		})
	}
}

func TestWechatMessageService_shouldFallbackStructured(t *testing.T) {
	service := &WechatMessageService{}

	tests := []struct {
		name           string
		result         *MessageSendResult
		originalUserID string
		originalGameID string
		isMapped       bool
		expected       bool
	}{
		{
			name:           "用户映射且用户拒绝消息-43101",
			result:         NewErrorResult(43101, "用户拒绝接受消息"),
			originalUserID: "user123",
			originalGameID: "game456",
			isMapped:       true,
			expected:       true,
		},
		{
			name:           "用户映射且用户拒绝消息-43108",
			result:         NewErrorResult(43108, "用户拒绝接受消息"),
			originalUserID: "user123",
			originalGameID: "game456",
			isMapped:       true,
			expected:       true,
		},
		{
			name:           "用户映射但发送成功",
			result:         NewSuccessResult(),
			originalUserID: "user123",
			originalGameID: "game456",
			isMapped:       true,
			expected:       false,
		},
		{
			name:           "未进行用户映射",
			result:         NewErrorResult(43101, "用户拒绝接受消息"),
			originalUserID: "user123",
			originalGameID: "game456",
			isMapped:       false,
			expected:       false,
		},
		{
			name:           "缺少原始用户信息",
			result:         NewErrorResult(43101, "用户拒绝接受消息"),
			originalUserID: "",
			originalGameID: "game456",
			isMapped:       true,
			expected:       false,
		},
		{
			name:           "缺少原始游戏信息",
			result:         NewErrorResult(43101, "用户拒绝接受消息"),
			originalUserID: "user123",
			originalGameID: "",
			isMapped:       true,
			expected:       false,
		},
		{
			name:           "其他错误不需要回退",
			result:         NewErrorResult(40003, "touser字段openid为空"),
			originalUserID: "user123",
			originalGameID: "game456",
			isMapped:       true,
			expected:       false,
		},
		{
			name:           "结果为nil",
			result:         nil,
			originalUserID: "user123",
			originalGameID: "game456",
			isMapped:       true,
			expected:       false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			actual := service.shouldFallbackStructured(tt.result, tt.originalUserID, tt.originalGameID, tt.isMapped)
			assert.Equal(t, tt.expected, actual)
		})
	}
}

func TestWechatMessageService_parseErrorCodeFromResult(t *testing.T) {
	service := &WechatMessageService{}

	tests := []struct {
		name     string
		result   string
		expected int32
	}{
		{
			name:     "成功情况-空字符串",
			result:   "",
			expected: 0,
		},
		{
			name:     "用户拒绝-43101",
			result:   "code: 43101, msg: 用户拒绝接受消息",
			expected: 43101,
		},
		{
			name:     "用户拒绝-43108",
			result:   "code: 43108, msg: 用户拒绝接受消息",
			expected: 43108,
		},
		{
			name:     "其他错误-40003",
			result:   "code: 40003, msg: touser字段openid为空",
			expected: 40003,
		},
		{
			name:     "格式异常-无法解析",
			result:   "invalid format",
			expected: -1,
		},
		{
			name:     "带空格的格式",
			result:   "code:  43101  , msg: test",
			expected: 43101,
		},
		{
			name:     "fallback到字符串匹配-43101",
			result:   "some prefix code: 43101 some suffix",
			expected: 43101,
		},
		{
			name:     "fallback到字符串匹配-43108",
			result:   "error code: 43108 user refused",
			expected: 43108,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			actual := service.parseErrorCodeFromResult(tt.result)
			assert.Equal(t, tt.expected, actual)
		})
	}
}

func TestFallbackResult_ToLegacyString(t *testing.T) {
	tests := []struct {
		name     string
		result   *FallbackResult
		expected string
	}{
		{
			name: "成功-无回退",
			result: &FallbackResult{
				MessageSendResult: NewSuccessResult(),
				HasFallback:       false,
			},
			expected: "",
		},
		{
			name: "成功-有回退",
			result: &FallbackResult{
				MessageSendResult: NewSuccessResult(),
				HasFallback:       true,
				FallbackMsg:       "通过回退发送成功",
			},
			expected: "",
		},
		{
			name: "失败-用户拒绝",
			result: &FallbackResult{
				MessageSendResult: NewErrorResult(43101, "用户拒绝接受消息"),
				HasFallback:       true,
				FallbackMsg:       "回退发送仍然失败",
			},
			expected: "code: 43101, msg: 用户拒绝接受消息",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			actual := tt.result.ToLegacyString()
			assert.Equal(t, tt.expected, actual)
		})
	}
}

func TestFallbackResult_ToDetailedString(t *testing.T) {
	tests := []struct {
		name     string
		result   *FallbackResult
		expected string
	}{
		{
			name: "成功-无回退",
			result: &FallbackResult{
				MessageSendResult: NewSuccessResult(),
				HasFallback:       false,
			},
			expected: "success",
		},
		{
			name: "成功-有回退",
			result: &FallbackResult{
				MessageSendResult: NewSuccessResult(),
				HasFallback:       true,
				FallbackMsg:       "通过回退发送成功",
			},
			expected: "success_with_fallback: 通过回退发送成功",
		},
		{
			name: "失败-无回退",
			result: &FallbackResult{
				MessageSendResult: NewErrorResult(40003, "openid错误"),
				HasFallback:       false,
			},
			expected: "code: 40003, msg: openid错误",
		},
		{
			name: "失败-有回退",
			result: &FallbackResult{
				MessageSendResult: NewErrorResult(43101, "用户拒绝接受消息"),
				HasFallback:       true,
				FallbackMsg:       "回退发送仍然失败",
			},
			expected: "failed_after_fallback: code: 43101, msg: 用户拒绝接受消息, fallback: 回退发送仍然失败",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			actual := tt.result.ToDetailedString()
			assert.Equal(t, tt.expected, actual)
		})
	}
}

func TestWechatMessageService_shouldFallback(t *testing.T) {
	service := &WechatMessageService{}

	tests := []struct {
		name           string
		result         string
		originalUserID string
		originalGameID string
		isMapped       bool
		expected       bool
	}{
		{
			name:           "不需要回退 - 没有错误",
			result:         "",
			originalUserID: "user123",
			originalGameID: "game123",
			isMapped:       true,
			expected:       false,
		},
		{
			name:           "不需要回退 - 没有进行映射",
			result:         "code: 43101, msg: user refuse",
			originalUserID: "user123",
			originalGameID: "game123",
			isMapped:       false,
			expected:       false,
		},
		{
			name:           "不需要回退 - 缺少原始用户ID",
			result:         "code: 43101, msg: user refuse",
			originalUserID: "",
			originalGameID: "game123",
			isMapped:       true,
			expected:       false,
		},
		{
			name:           "不需要回退 - 缺少原始游戏ID",
			result:         "code: 43101, msg: user refuse",
			originalUserID: "user123",
			originalGameID: "",
			isMapped:       true,
			expected:       false,
		},
		{
			name:           "需要回退 - 43101错误码",
			result:         "code: 43101, msg: user refuse",
			originalUserID: "user123",
			originalGameID: "game123",
			isMapped:       true,
			expected:       true,
		},
		{
			name:           "需要回退 - 43108错误码",
			result:         "code: 43108, msg: user refuse",
			originalUserID: "user123",
			originalGameID: "game123",
			isMapped:       true,
			expected:       true,
		},
		{
			name:           "不需要回退 - 其他错误码",
			result:         "code: 40001, msg: other error",
			originalUserID: "user123",
			originalGameID: "game123",
			isMapped:       true,
			expected:       false,
		},
		{
			name:           "需要回退 - 字符串匹配fallback",
			result:         "error occurred: code: 43101 user refused",
			originalUserID: "user123",
			originalGameID: "game123",
			isMapped:       true,
			expected:       true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.shouldFallback(tt.result, tt.originalUserID, tt.originalGameID, tt.isMapped)
			assert.Equal(t, tt.expected, result)
		})
	}
}
