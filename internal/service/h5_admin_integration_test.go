package service

import (
	"context"
	"os"
	"testing"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/mysql"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/redis"
	"github.com/stretchr/testify/assert"
)

// 初始化测试环境
func initTestEnvForH5AdminIntegration() {
	// 设置配置文件路径到项目根目录
	originalDir, _ := os.Getwd()
	defer os.Chdir(originalDir)

	// 切换到项目根目录
	if err := os.Chdir("../../"); err != nil {
		panic("无法切换到项目根目录: " + err.Error())
	}

	config.MustInit()
	logger.InitLogger(&config.GlobConfig.Logger)
	mysql.InitMysql(&config.GlobConfig.Mysql)
	store.InitQueryDB()
	redis.InitRedis(&config.GlobConfig.Redis)
}

func TestH5AdminAuthService_LoginWithDateIntegration(t *testing.T) {
	initTestEnvForH5AdminIntegration()

	service := SingletonH5AdminAuthService()
	ctx := context.Background()

	// 注意：这个测试需要数据库中有测试用户
	// 如果没有测试用户，可以先创建一个
	req := &bean.LoginRequest{
		Username: "test_user", // 需要确保这个用户存在
		Password: "test_password",
	}

	// 由于我们不确定测试用户是否存在，这里只测试响应结构
	// 实际项目中应该先创建测试用户
	resp, err := service.Login(ctx, req)

	// 如果用户不存在，会返回错误，这是正常的
	if err != nil {
		t.Logf("登录失败（可能是测试用户不存在）: %v", err)
		return
	}

	// 获取今天是星期几
	beijingLocation, err := time.LoadLocation("Asia/Shanghai")
	assert.NoError(t, err)
	today := time.Now().In(beijingLocation)
	weekday := today.Weekday()

	// 如果今天是周五、周六或周日，应该返回true（除非数据库中有特殊配置）
	expectedPlayable := weekday == time.Friday || weekday == time.Saturday || weekday == time.Sunday

	t.Logf("今天是 %s，预期可玩状态: %v，实际可玩状态: %v",
		weekday.String(), expectedPlayable, resp.IsPlayable)
}
