package service

import (
	"context"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"gorm.io/gorm"
)

// RealtimeRechargeSummaryService 负责实时更新 m_vip_user_recharge_summary 表（去除 manage-console 定时任务后的替代方案）
// 仅在订单首次进入 PaymentProductShipmentSuccess 状态时累计金额与订单次数。
type RealtimeRechargeSummaryService struct{}

var realtimeRechargeSummaryService = &RealtimeRechargeSummaryService{}

func SingletonRealtimeRechargeSummaryService() *RealtimeRechargeSummaryService {
	return realtimeRechargeSummaryService
}

// HandleOrderShipmentSuccess 在产品发货(订单状态成功)后执行增量统计。
// 调用方需传入订单原始状态 originalStatus 与成功后的订单信息。
// 幂等：只有当 originalStatus != successStatus 时才进行汇总更新。
// 优化：只有用户总充值金额达到VIP门槛时才进行统计入库。
func (s *RealtimeRechargeSummaryService) HandleOrderShipmentSuccess(ctx context.Context, orderID string, originalStatus int32, successStatus int32) {
	if originalStatus == successStatus { // 已是成功状态，不重复统计
		logger.Logger.DebugfCtx(ctx, "order already success, skip summary order_id=%s", orderID)
		return
	}

	// 查询订单详情（再次读取，避免上层未提供完整字段）
	q := store.QueryDB().AOrder
	order, err := q.WithContext(ctx).Where(q.OrderID.Eq(orderID)).Where(q.IsDeleted.Is(false)).First()
	if err != nil {
		if err != gorm.ErrRecordNotFound {
			logger.Logger.ErrorfCtx(ctx, "HandleOrderShipmentSuccess query order failed: %v", err)
		}
		return
	}
	if order.Status != successStatus { // 双重检查，确保已成功
		logger.Logger.DebugfCtx(ctx, "order not in success status after update, skip summary order_id=%s status=%d", orderID, order.Status)
		return
	}
	if order.Money <= 0 { // 金额为0不统计
		return
	}

	// 先查询VIP规则；未配置则直接跳过后续处理
	vipRule, err := s.getVipRuleByGameID(ctx, order.GameID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "HandleOrderShipmentSuccess query vip rule failed: game_id=%s err=%v", order.GameID, err)
		return
	}
	if vipRule == nil {
		logger.Logger.DebugfCtx(ctx, "no vip rule found for game_id=%s, skip recharge summary", order.GameID)
		return
	}

	// 计算用户当前累计充值（分），用于：1) VIP门槛判断；2) 首次插入时初始化 total_amount 为历史累计
	var (
		shouldCreateVIP         bool
		currentTotalAmountCents int64
		currentTotalAmountYuan  float64
		vipThresholdAmountYuan  float64
	)
	// 计算累计（分）：优先使用汇总表，否则回退订单表聚合
	if totalCents, err := s.getUserTotalAmountOptimized(ctx, order.UserID, int64(order.Money)); err != nil {
		logger.Logger.ErrorfCtx(ctx, "HandleOrderShipmentSuccess get user total amount failed: user_id=%s game_id=%s err=%v", order.UserID, order.GameID, err)
	} else {
		currentTotalAmountCents = totalCents
		currentTotalAmountYuan = float64(totalCents) / 100.0
	}

	// VIP 门槛判断（单位：元）
	vipThresholdAmountYuan = float64(vipRule.MinRechargeAmount)
	if currentTotalAmountYuan >= vipThresholdAmountYuan {
		shouldCreateVIP = true
	} else {
		logger.Logger.DebugfCtx(ctx, "user total recharge %.2f not reach vip threshold %.2f: user_id=%s game_id=%s", currentTotalAmountYuan, vipThresholdAmountYuan, order.UserID, order.GameID)
	}

	// 先进行充值汇总（无论是否达到VIP门槛都入库）
	platformType := payTypeString(order.PayType)
	amountCents := int64(order.Money) // 使用分为单位，转换为int64
	now := time.Now().UnixMilli()
	summary := &model.MVipUserRechargeSummary{
		UserID:          order.UserID,
		GameID:          order.GameID,
		PlatformType:    platformType,
		TotalAmount:     amountCents,
		FirstRechargeAt: order.CreatedAt,
		LastRechargeAt:  order.CreatedAt,
		LastSyncAt:      now,
		CreatedAt:       now,
		UpdatedAt:       now,
	}
	if err := s.upsertRechargeSummaryIncrement(ctx, summary, currentTotalAmountCents); err != nil {
		logger.Logger.ErrorfCtx(ctx, "recharge summary upsert failed: order_id=%s err=%v", orderID, err)
		return
	}

	// 达到门槛再创建VIP用户（幂等）
	if shouldCreateVIP {
		if err := SingletonVIPService().CreateVIPUser(ctx, order.UserID, order.GameID, platformType); err != nil {
			logger.Logger.ErrorfCtx(ctx, "HandleOrderShipmentSuccess create vip user failed: user_id=%s game_id=%s err=%v", order.UserID, order.GameID, err)
		}
	}

	logger.Logger.InfofCtx(ctx, "recharge summary upsert success: order_id=%s add_amount=%.2f total_amount=%.2f vip_threshold=%.2f", orderID, float64(amountCents)/100.0, currentTotalAmountYuan, vipThresholdAmountYuan)
}

func payTypeString(p int32) string {
	switch p {
	case 1, 2:
		return "minigame"
	case 5, 6:
		return "douyin_minigame"
	default:
		return "Unknown"
	}
}

// upsertRechargeSummaryIncrement 以安全的 UPDATE→INSERT→(冲突)UPDATE 流程实现原子增量，避免 OnConflict + gorm.Expr
// insertTotalAmount 表示当首次插入该用户记录时使用的历史累计总额（单位：分，包含本次订单）。
func (s *RealtimeRechargeSummaryService) upsertRechargeSummaryIncrement(ctx context.Context, row *model.MVipUserRechargeSummary, insertTotalAmount int64) error {
	// 先尝试更新已存在的记录（按 user_id 汇总所有游戏）
	res := store.GOrmDB(ctx).Exec(
		`UPDATE m_vip_user_recharge_summary
		SET total_amount = total_amount + ?,
		    last_recharge_at = GREATEST(last_recharge_at, ?),
		    last_sync_at = ?,
		    updated_at = ?
		WHERE user_id = ?`,
		row.TotalAmount,
		row.LastRechargeAt,
		row.LastSyncAt,
		row.UpdatedAt,
		row.UserID,
	)
	if res.Error != nil {
		return res.Error
	}
	if res.RowsAffected > 0 {
		return nil
	}

	// 不存在则插入新纪录（用历史累计总额初始化 total_amount）
	ins := store.GOrmDB(ctx).Exec(
		`INSERT INTO m_vip_user_recharge_summary
			 (user_id, game_id, platform_type, total_amount, first_recharge_at, last_recharge_at, last_sync_at, created_at, updated_at)
			 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
		row.UserID,
		row.GameID,
		row.PlatformType,
		insertTotalAmount,
		row.FirstRechargeAt,
		row.LastRechargeAt,
		row.LastSyncAt,
		row.CreatedAt,
		row.UpdatedAt,
	)
	if ins.Error == nil {
		return nil
	}

	// 若插入因并发导致唯一键冲突，则回退再 UPDATE 一次
	res2 := store.GOrmDB(ctx).Exec(
		`UPDATE m_vip_user_recharge_summary
		SET total_amount = total_amount + ?,
		    last_recharge_at = GREATEST(last_recharge_at, ?),
		    last_sync_at = ?,
		    updated_at = ?
		WHERE user_id = ?`,
		row.TotalAmount,
		row.LastRechargeAt,
		row.LastSyncAt,
		row.UpdatedAt,
		row.UserID,
	)
	if res2.Error != nil {
		return res2.Error
	}
	return nil
}

// getVipRuleByGameID 根据游戏ID查询VIP规则（使用GORM Gen生成的查询接口）
func (s *RealtimeRechargeSummaryService) getVipRuleByGameID(ctx context.Context, gameID string) (*model.MVipAssignmentRule, error) {
	// 使用GORM Gen生成的查询接口，自动依赖 idx_game_active_deleted 索引
	q := store.QueryDB().MVipAssignmentRule
	rule, err := q.WithContext(ctx).
		Where(q.GameID.Eq(gameID)).
		Where(q.IsActive.Is(true)).
		Where(q.IsDeleted.Is(false)).
		First()

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			logger.Logger.DebugfCtx(ctx, "vip rule not found: game_id=%s", gameID)
			return nil, nil
		}
		return nil, err
	}

	logger.Logger.DebugfCtx(ctx, "vip rule queried: game_id=%s threshold=%.2f", gameID, float64(rule.MinRechargeAmount))
	return rule, nil
}

// getUserTotalAmountOptimized 优化的用户总充值金额计算（以分为单位）
// 优先从汇总表获取现有金额，只有在必要时才进行全量计算
func (s *RealtimeRechargeSummaryService) getUserTotalAmountOptimized(ctx context.Context, userID string, currentOrderAmountCents int64) (int64, error) {
	// 使用GORM Gen查询接口查询汇总表
	summaryQuery := store.QueryDB().MVipUserRechargeSummary
	existing, err := summaryQuery.WithContext(ctx).
		Select(summaryQuery.TotalAmount).
		Where(summaryQuery.UserID.Eq(userID)).
		// Where(summaryQuery.GameID.Eq(gameID)).
		First()

	if err != nil && err != gorm.ErrRecordNotFound {
		return 0, err
	}

	if err == gorm.ErrRecordNotFound {
		// 汇总表中没有记录，需要从订单表计算历史总金额
		historicalTotalCents, err := s.calculateUserTotalRecharge(ctx, userID)
		if err != nil {
			return 0, err
		}
		return historicalTotalCents, nil
	}

	// 汇总表有记录，返回现有金额 + 当前订单金额（都是分为单位）
	return existing.TotalAmount + currentOrderAmountCents, nil
}

// calculateUserTotalRecharge 计算用户的总充值金额（所有游戏汇总，使用GORM Gen查询接口，返回分为单位）
func (s *RealtimeRechargeSummaryService) calculateUserTotalRecharge(ctx context.Context, userID string) (int64, error) {
	// 查询用户所有成功订单的总金额（跨所有游戏汇总，已经是分为单位）
	var totalMoney int64
	db := store.GOrmDB(ctx).
		Model(&model.AOrder{}).
		Where("user_id = ? AND status = ? AND is_deleted = 0", userID, 4).
		Select("COALESCE(SUM(money), 0)").
		Scan(&totalMoney)

	if db.Error != nil {
		return 0, db.Error
	}

	// 直接返回分为单位的金额
	return totalMoney, nil
}

// // SuggestRechargeSummaryIndexes 返回需要的索引（供文档/初始化使用）
// func SuggestRechargeSummaryIndexes() []string {
// 	return []string{
// 		"ALTER TABLE m_vip_user_recharge_summary ADD UNIQUE KEY IF NOT EXISTS uk_user_game (user_id, game_id)",
// 		"ALTER TABLE a_order ADD INDEX IF NOT EXISTS idx_user_game_status_created (user_id, game_id, status, created_at)",
// 	}
// }

// // DebugCheckSummaryRow 用于调试：返回当前汇总行
// func DebugCheckSummaryRow(ctx context.Context, userID, gameID string) (float64, int32, error) {
// 	var totalAmount float64
// 	var orderCount int32
// 	row := store.GOrmDB(ctx).Raw("SELECT total_amount, order_count FROM m_vip_user_recharge_summary WHERE user_id=? AND game_id=?", userID, gameID).Row()
// 	if err := row.Err(); err != nil {
// 		return 0, 0, err
// 	}
// 	if err := row.Scan(&totalAmount, &orderCount); err != nil {
// 		return 0, 0, err
// 	}
// 	return totalAmount, orderCount, nil
// }

// // Optional: 批量回填工具（当需要从历史订单重建时）
// func BackfillRechargeSummary(ctx context.Context, startAfterID int32, batchSize int) error {
// 	if batchSize <= 0 {
// 		batchSize = 500
// 	}
// 	q := store.QueryDB().AOrder
// 	var lastID = startAfterID
// 	for {
// 		orders, err := q.WithContext(ctx).
// 			Where(q.ID.Gt(lastID)).
// 			Where(q.Status.Eq(4)). // PaymentProductShipmentSuccess 假定=4
// 			Where(q.IsDeleted.Is(false)).
// 			Order(q.ID.Asc()).
// 			Limit(batchSize).Find()
// 		if err != nil {
// 			return err
// 		}
// 		if len(orders) == 0 {
// 			break
// 		}
// 		for _, o := range orders {
// 			SingletonRealtimeRechargeSummaryService().HandleOrderShipmentSuccess(ctx, o.OrderID, 0, o.Status) // originalStatus 传0 视为需要统计
// 			lastID = o.ID
// 		}
// 		if len(orders) < batchSize {
// 			break
// 		}
// 	}
// 	return nil
// }

// // 简单自检：验证唯一键是否存在
// func CheckSummaryUniqueKey(ctx context.Context) error {
// 	var cnt int
// 	err := store.GOrmDB(ctx).Raw("SELECT COUNT(1) FROM information_schema.statistics WHERE table_schema = DATABASE() AND table_name='m_vip_user_recharge_summary' AND index_name='uk_user_game'").Scan(&cnt).Error
// 	if err != nil {
// 		return err
// 	}
// 	if cnt == 0 {
// 		return fmt.Errorf("unique key uk_user_game missing on m_vip_user_recharge_summary")
// 	}
// 	return nil
// }
