// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMVipUserRechargeSummary = "m_vip_user_recharge_summary"

// MVipUserRechargeSummary mapped from table <m_vip_user_recharge_summary>
type MVipUserRechargeSummary struct {
	ID              int64  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	UserID          string `gorm:"column:user_id;not null;comment:用户ID" json:"user_id"`                       // 用户ID
	GameID          string `gorm:"column:game_id;not null;comment:游戏ID" json:"game_id"`                       // 游戏ID
	PlatformType    string `gorm:"column:platform_type;not null;comment:平台类型" json:"platform_type"`           // 平台类型
	TotalAmount     int64  `gorm:"column:total_amount;not null;comment:累计充值金额(分)" json:"total_amount"`        // 累计充值金额(分)
	FirstRechargeAt int64  `gorm:"column:first_recharge_at;not null;comment:首次充值时间" json:"first_recharge_at"` // 首次充值时间
	LastRechargeAt  int64  `gorm:"column:last_recharge_at;comment:最后充值时间" json:"last_recharge_at"`            // 最后充值时间
	LastSyncAt      int64  `gorm:"column:last_sync_at;comment:最后同步时间" json:"last_sync_at"`                    // 最后同步时间
	CreatedAt       int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt       int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
}

// TableName MVipUserRechargeSummary's table name
func (*MVipUserRechargeSummary) TableName() string {
	return TableNameMVipUserRechargeSummary
}
