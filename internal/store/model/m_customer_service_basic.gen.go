// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMCustomerServiceBasic = "m_customer_service_basic"

// MCustomerServiceBasic mapped from table <m_customer_service_basic>
type MCustomerServiceBasic struct {
	ID          int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	ServiceID   string `gorm:"column:service_id;not null;comment:客服ID" json:"service_id"`     // 客服ID
	ServiceName string `gorm:"column:service_name;not null;comment:客服名称" json:"service_name"` // 客服名称
	IsActive    bool   `gorm:"column:is_active;default:1;comment:是否激活" json:"is_active"`      // 是否激活
	CreatedAt   int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt   int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted   bool   `gorm:"column:is_deleted" json:"is_deleted"`
}

// TableName MCustomerServiceBasic's table name
func (*MCustomerServiceBasic) TableName() string {
	return TableNameMCustomerServiceBasic
}
