// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameMVipAssignmentRule = "m_vip_assignment_rule"

// MVipAssignmentRule mapped from table <m_vip_assignment_rule>
type MVipAssignmentRule struct {
	ID                int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	GameID            string `gorm:"column:game_id;not null;comment:游戏ID" json:"game_id"`                                // 游戏ID
	MinRechargeAmount int32  `gorm:"column:min_recharge_amount;not null;comment:最小充值金额门槛(元)" json:"min_recharge_amount"` // 最小充值金额门槛(元)
	PopupEnabled      bool   `gorm:"column:popup_enabled;comment:弹窗开关:0-关闭,1-开启" json:"popup_enabled"`                   // 弹窗开关:0-关闭,1-开启
	PopupImageURL     string `gorm:"column:popup_image_url;comment:弹窗图片URL(客服二维码)" json:"popup_image_url"`               // 弹窗图片URL(客服二维码)
	Description       string `gorm:"column:description;comment:规则描述" json:"description"`                                 // 规则描述
	IsActive          bool   `gorm:"column:is_active;default:1;comment:规则是否激活" json:"is_active"`                         // 规则是否激活
	CreatedAt         int64  `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt         int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted         bool   `gorm:"column:is_deleted" json:"is_deleted"`
}

// TableName MVipAssignmentRule's table name
func (*MVipAssignmentRule) TableName() string {
	return TableNameMVipAssignmentRule
}
