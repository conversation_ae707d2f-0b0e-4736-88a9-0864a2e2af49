// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameMVipUser = "m_vip_user"

// MVipUser mapped from table <m_vip_user>
type MVipUser struct {
	ID                   int32     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	UserID               string    `gorm:"column:user_id;not null;comment:用户ID" json:"user_id"`                                          // 用户ID
	GameID               string    `gorm:"column:game_id;not null;comment:游戏ID" json:"game_id"`                                          // 游戏ID
	PlatformType         string    `gorm:"column:platform_type;not null;comment:平台类型" json:"platform_type"`                              // 平台类型
	CustomerServiceID    string    `gorm:"column:customer_service_id;default:10001;comment:分配的客服ID，固定值10001" json:"customer_service_id"` // 分配的客服ID，固定值10001
	IsAuthenticated      bool      `gorm:"column:is_authenticated;comment:是否已认证:0-否,1-是" json:"is_authenticated"`                        // 是否已认证:0-否,1-是
	AssignedAt           int64     `gorm:"column:assigned_at;comment:分配时间" json:"assigned_at"`                                           // 分配时间
	AuthenticatedAt      int64     `gorm:"column:authenticated_at;comment:认证时间" json:"authenticated_at"`                                 // 认证时间
	LastPopupDate        time.Time `gorm:"column:last_popup_date;not null;default:1900-01-01" json:"last_popup_date"`
	AuthenticationRemark string    `gorm:"column:authentication_remark;not null;comment:认证备注" json:"authentication_remark"` // 认证备注
	CreatedAt            int64     `gorm:"column:created_at;not null;autoCreateTime:milli" json:"created_at"`
	UpdatedAt            int64     `gorm:"column:updated_at;not null;autoUpdateTime:milli" json:"updated_at"`
	IsDeleted            bool      `gorm:"column:is_deleted;not null" json:"is_deleted"`
}

// TableName MVipUser's table name
func (*MVipUser) TableName() string {
	return TableNameMVipUser
}
