// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameH5PlayableDate = "h5_playable_dates"

// H5PlayableDate H5可玩日期表
type H5PlayableDate struct {
	ID           int32  `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                      // 主键ID
	PlayableDate string `gorm:"column:playable_date;not null;comment:可玩日期（北京时区）" json:"playable_date"`               // 可玩日期（北京时区）
	IsPlayable   int32  `gorm:"column:is_playable;not null;comment:是否可玩" json:"is_playable"`                         // 是否可玩
	Description  string `gorm:"column:description;not null;comment:日期描述信息" json:"description"`                       // 日期描述信息
	CreatorID    string `gorm:"column:creator_id;not null;comment:创建人ID" json:"creator_id"`                          // 创建人ID
	CreatedAt    int64  `gorm:"column:created_at;not null;autoCreateTime:milli;comment:创建时间戳（毫秒）" json:"created_at"` // 创建时间戳（毫秒）
	UpdatedAt    int64  `gorm:"column:updated_at;not null;autoUpdateTime:milli;comment:更新时间戳（毫秒）" json:"updated_at"` // 更新时间戳（毫秒）
	IsDeleted    bool   `gorm:"column:is_deleted;not null;comment:逻辑删除，0=未删，1=已删" json:"is_deleted"`                 // 逻辑删除，0=未删，1=已删
}

// TableName H5PlayableDate's table name
func (*H5PlayableDate) TableName() string {
	return TableNameH5PlayableDate
}
