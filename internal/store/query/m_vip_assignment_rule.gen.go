// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMVipAssignmentRule(db *gorm.DB, opts ...gen.DOOption) mVipAssignmentRule {
	_mVipAssignmentRule := mVipAssignmentRule{}

	_mVipAssignmentRule.mVipAssignmentRuleDo.UseDB(db, opts...)
	_mVipAssignmentRule.mVipAssignmentRuleDo.UseModel(&model.MVipAssignmentRule{})

	tableName := _mVipAssignmentRule.mVipAssignmentRuleDo.TableName()
	_mVipAssignmentRule.ALL = field.NewAsterisk(tableName)
	_mVipAssignmentRule.ID = field.NewInt32(tableName, "id")
	_mVipAssignmentRule.GameID = field.NewString(tableName, "game_id")
	_mVipAssignmentRule.MinRechargeAmount = field.NewInt32(tableName, "min_recharge_amount")
	_mVipAssignmentRule.PopupEnabled = field.NewBool(tableName, "popup_enabled")
	_mVipAssignmentRule.PopupImageURL = field.NewString(tableName, "popup_image_url")
	_mVipAssignmentRule.Description = field.NewString(tableName, "description")
	_mVipAssignmentRule.IsActive = field.NewBool(tableName, "is_active")
	_mVipAssignmentRule.CreatedAt = field.NewInt64(tableName, "created_at")
	_mVipAssignmentRule.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mVipAssignmentRule.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mVipAssignmentRule.fillFieldMap()

	return _mVipAssignmentRule
}

type mVipAssignmentRule struct {
	mVipAssignmentRuleDo

	ALL               field.Asterisk
	ID                field.Int32
	GameID            field.String // 游戏ID
	MinRechargeAmount field.Int32  // 最小充值金额门槛(元)
	PopupEnabled      field.Bool   // 弹窗开关:0-关闭,1-开启
	PopupImageURL     field.String // 弹窗图片URL(客服二维码)
	Description       field.String // 规则描述
	IsActive          field.Bool   // 规则是否激活
	CreatedAt         field.Int64
	UpdatedAt         field.Int64
	IsDeleted         field.Bool

	fieldMap map[string]field.Expr
}

func (m mVipAssignmentRule) Table(newTableName string) *mVipAssignmentRule {
	m.mVipAssignmentRuleDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mVipAssignmentRule) As(alias string) *mVipAssignmentRule {
	m.mVipAssignmentRuleDo.DO = *(m.mVipAssignmentRuleDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mVipAssignmentRule) updateTableName(table string) *mVipAssignmentRule {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.GameID = field.NewString(table, "game_id")
	m.MinRechargeAmount = field.NewInt32(table, "min_recharge_amount")
	m.PopupEnabled = field.NewBool(table, "popup_enabled")
	m.PopupImageURL = field.NewString(table, "popup_image_url")
	m.Description = field.NewString(table, "description")
	m.IsActive = field.NewBool(table, "is_active")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mVipAssignmentRule) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mVipAssignmentRule) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 10)
	m.fieldMap["id"] = m.ID
	m.fieldMap["game_id"] = m.GameID
	m.fieldMap["min_recharge_amount"] = m.MinRechargeAmount
	m.fieldMap["popup_enabled"] = m.PopupEnabled
	m.fieldMap["popup_image_url"] = m.PopupImageURL
	m.fieldMap["description"] = m.Description
	m.fieldMap["is_active"] = m.IsActive
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mVipAssignmentRule) clone(db *gorm.DB) mVipAssignmentRule {
	m.mVipAssignmentRuleDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mVipAssignmentRule) replaceDB(db *gorm.DB) mVipAssignmentRule {
	m.mVipAssignmentRuleDo.ReplaceDB(db)
	return m
}

type mVipAssignmentRuleDo struct{ gen.DO }

type IMVipAssignmentRuleDo interface {
	gen.SubQuery
	Debug() IMVipAssignmentRuleDo
	WithContext(ctx context.Context) IMVipAssignmentRuleDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMVipAssignmentRuleDo
	WriteDB() IMVipAssignmentRuleDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMVipAssignmentRuleDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMVipAssignmentRuleDo
	Not(conds ...gen.Condition) IMVipAssignmentRuleDo
	Or(conds ...gen.Condition) IMVipAssignmentRuleDo
	Select(conds ...field.Expr) IMVipAssignmentRuleDo
	Where(conds ...gen.Condition) IMVipAssignmentRuleDo
	Order(conds ...field.Expr) IMVipAssignmentRuleDo
	Distinct(cols ...field.Expr) IMVipAssignmentRuleDo
	Omit(cols ...field.Expr) IMVipAssignmentRuleDo
	Join(table schema.Tabler, on ...field.Expr) IMVipAssignmentRuleDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMVipAssignmentRuleDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMVipAssignmentRuleDo
	Group(cols ...field.Expr) IMVipAssignmentRuleDo
	Having(conds ...gen.Condition) IMVipAssignmentRuleDo
	Limit(limit int) IMVipAssignmentRuleDo
	Offset(offset int) IMVipAssignmentRuleDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMVipAssignmentRuleDo
	Unscoped() IMVipAssignmentRuleDo
	Create(values ...*model.MVipAssignmentRule) error
	CreateInBatches(values []*model.MVipAssignmentRule, batchSize int) error
	Save(values ...*model.MVipAssignmentRule) error
	First() (*model.MVipAssignmentRule, error)
	Take() (*model.MVipAssignmentRule, error)
	Last() (*model.MVipAssignmentRule, error)
	Find() ([]*model.MVipAssignmentRule, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MVipAssignmentRule, err error)
	FindInBatches(result *[]*model.MVipAssignmentRule, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MVipAssignmentRule) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMVipAssignmentRuleDo
	Assign(attrs ...field.AssignExpr) IMVipAssignmentRuleDo
	Joins(fields ...field.RelationField) IMVipAssignmentRuleDo
	Preload(fields ...field.RelationField) IMVipAssignmentRuleDo
	FirstOrInit() (*model.MVipAssignmentRule, error)
	FirstOrCreate() (*model.MVipAssignmentRule, error)
	FindByPage(offset int, limit int) (result []*model.MVipAssignmentRule, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMVipAssignmentRuleDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mVipAssignmentRuleDo) Debug() IMVipAssignmentRuleDo {
	return m.withDO(m.DO.Debug())
}

func (m mVipAssignmentRuleDo) WithContext(ctx context.Context) IMVipAssignmentRuleDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mVipAssignmentRuleDo) ReadDB() IMVipAssignmentRuleDo {
	return m.Clauses(dbresolver.Read)
}

func (m mVipAssignmentRuleDo) WriteDB() IMVipAssignmentRuleDo {
	return m.Clauses(dbresolver.Write)
}

func (m mVipAssignmentRuleDo) Session(config *gorm.Session) IMVipAssignmentRuleDo {
	return m.withDO(m.DO.Session(config))
}

func (m mVipAssignmentRuleDo) Clauses(conds ...clause.Expression) IMVipAssignmentRuleDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mVipAssignmentRuleDo) Returning(value interface{}, columns ...string) IMVipAssignmentRuleDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mVipAssignmentRuleDo) Not(conds ...gen.Condition) IMVipAssignmentRuleDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mVipAssignmentRuleDo) Or(conds ...gen.Condition) IMVipAssignmentRuleDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mVipAssignmentRuleDo) Select(conds ...field.Expr) IMVipAssignmentRuleDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mVipAssignmentRuleDo) Where(conds ...gen.Condition) IMVipAssignmentRuleDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mVipAssignmentRuleDo) Order(conds ...field.Expr) IMVipAssignmentRuleDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mVipAssignmentRuleDo) Distinct(cols ...field.Expr) IMVipAssignmentRuleDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mVipAssignmentRuleDo) Omit(cols ...field.Expr) IMVipAssignmentRuleDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mVipAssignmentRuleDo) Join(table schema.Tabler, on ...field.Expr) IMVipAssignmentRuleDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mVipAssignmentRuleDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMVipAssignmentRuleDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mVipAssignmentRuleDo) RightJoin(table schema.Tabler, on ...field.Expr) IMVipAssignmentRuleDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mVipAssignmentRuleDo) Group(cols ...field.Expr) IMVipAssignmentRuleDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mVipAssignmentRuleDo) Having(conds ...gen.Condition) IMVipAssignmentRuleDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mVipAssignmentRuleDo) Limit(limit int) IMVipAssignmentRuleDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mVipAssignmentRuleDo) Offset(offset int) IMVipAssignmentRuleDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mVipAssignmentRuleDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMVipAssignmentRuleDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mVipAssignmentRuleDo) Unscoped() IMVipAssignmentRuleDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mVipAssignmentRuleDo) Create(values ...*model.MVipAssignmentRule) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mVipAssignmentRuleDo) CreateInBatches(values []*model.MVipAssignmentRule, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mVipAssignmentRuleDo) Save(values ...*model.MVipAssignmentRule) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mVipAssignmentRuleDo) First() (*model.MVipAssignmentRule, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MVipAssignmentRule), nil
	}
}

func (m mVipAssignmentRuleDo) Take() (*model.MVipAssignmentRule, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MVipAssignmentRule), nil
	}
}

func (m mVipAssignmentRuleDo) Last() (*model.MVipAssignmentRule, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MVipAssignmentRule), nil
	}
}

func (m mVipAssignmentRuleDo) Find() ([]*model.MVipAssignmentRule, error) {
	result, err := m.DO.Find()
	return result.([]*model.MVipAssignmentRule), err
}

func (m mVipAssignmentRuleDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MVipAssignmentRule, err error) {
	buf := make([]*model.MVipAssignmentRule, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mVipAssignmentRuleDo) FindInBatches(result *[]*model.MVipAssignmentRule, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mVipAssignmentRuleDo) Attrs(attrs ...field.AssignExpr) IMVipAssignmentRuleDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mVipAssignmentRuleDo) Assign(attrs ...field.AssignExpr) IMVipAssignmentRuleDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mVipAssignmentRuleDo) Joins(fields ...field.RelationField) IMVipAssignmentRuleDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mVipAssignmentRuleDo) Preload(fields ...field.RelationField) IMVipAssignmentRuleDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mVipAssignmentRuleDo) FirstOrInit() (*model.MVipAssignmentRule, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MVipAssignmentRule), nil
	}
}

func (m mVipAssignmentRuleDo) FirstOrCreate() (*model.MVipAssignmentRule, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MVipAssignmentRule), nil
	}
}

func (m mVipAssignmentRuleDo) FindByPage(offset int, limit int) (result []*model.MVipAssignmentRule, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mVipAssignmentRuleDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mVipAssignmentRuleDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mVipAssignmentRuleDo) Delete(models ...*model.MVipAssignmentRule) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mVipAssignmentRuleDo) withDO(do gen.Dao) *mVipAssignmentRuleDo {
	m.DO = *do.(*gen.DO)
	return m
}
