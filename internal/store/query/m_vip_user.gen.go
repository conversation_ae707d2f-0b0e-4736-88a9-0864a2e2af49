// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMVipUser(db *gorm.DB, opts ...gen.DOOption) mVipUser {
	_mVipUser := mVipUser{}

	_mVipUser.mVipUserDo.UseDB(db, opts...)
	_mVipUser.mVipUserDo.UseModel(&model.MVipUser{})

	tableName := _mVipUser.mVipUserDo.TableName()
	_mVipUser.ALL = field.NewAsterisk(tableName)
	_mVipUser.ID = field.NewInt32(tableName, "id")
	_mVipUser.UserID = field.NewString(tableName, "user_id")
	_mVipUser.GameID = field.NewString(tableName, "game_id")
	_mVipUser.PlatformType = field.NewString(tableName, "platform_type")
	_mVipUser.CustomerServiceID = field.NewString(tableName, "customer_service_id")
	_mVipUser.IsAuthenticated = field.NewBool(tableName, "is_authenticated")
	_mVipUser.AssignedAt = field.NewInt64(tableName, "assigned_at")
	_mVipUser.AuthenticatedAt = field.NewInt64(tableName, "authenticated_at")
	_mVipUser.LastPopupDate = field.NewTime(tableName, "last_popup_date")
	_mVipUser.AuthenticationRemark = field.NewString(tableName, "authentication_remark")
	_mVipUser.CreatedAt = field.NewInt64(tableName, "created_at")
	_mVipUser.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mVipUser.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mVipUser.fillFieldMap()

	return _mVipUser
}

type mVipUser struct {
	mVipUserDo

	ALL                  field.Asterisk
	ID                   field.Int32
	UserID               field.String // 用户ID
	GameID               field.String // 游戏ID
	PlatformType         field.String // 平台类型
	CustomerServiceID    field.String // 分配的客服ID，固定值10001
	IsAuthenticated      field.Bool   // 是否已认证:0-否,1-是
	AssignedAt           field.Int64  // 分配时间
	AuthenticatedAt      field.Int64  // 认证时间
	LastPopupDate        field.Time
	AuthenticationRemark field.String // 认证备注
	CreatedAt            field.Int64
	UpdatedAt            field.Int64
	IsDeleted            field.Bool

	fieldMap map[string]field.Expr
}

func (m mVipUser) Table(newTableName string) *mVipUser {
	m.mVipUserDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mVipUser) As(alias string) *mVipUser {
	m.mVipUserDo.DO = *(m.mVipUserDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mVipUser) updateTableName(table string) *mVipUser {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.UserID = field.NewString(table, "user_id")
	m.GameID = field.NewString(table, "game_id")
	m.PlatformType = field.NewString(table, "platform_type")
	m.CustomerServiceID = field.NewString(table, "customer_service_id")
	m.IsAuthenticated = field.NewBool(table, "is_authenticated")
	m.AssignedAt = field.NewInt64(table, "assigned_at")
	m.AuthenticatedAt = field.NewInt64(table, "authenticated_at")
	m.LastPopupDate = field.NewTime(table, "last_popup_date")
	m.AuthenticationRemark = field.NewString(table, "authentication_remark")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mVipUser) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mVipUser) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 13)
	m.fieldMap["id"] = m.ID
	m.fieldMap["user_id"] = m.UserID
	m.fieldMap["game_id"] = m.GameID
	m.fieldMap["platform_type"] = m.PlatformType
	m.fieldMap["customer_service_id"] = m.CustomerServiceID
	m.fieldMap["is_authenticated"] = m.IsAuthenticated
	m.fieldMap["assigned_at"] = m.AssignedAt
	m.fieldMap["authenticated_at"] = m.AuthenticatedAt
	m.fieldMap["last_popup_date"] = m.LastPopupDate
	m.fieldMap["authentication_remark"] = m.AuthenticationRemark
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mVipUser) clone(db *gorm.DB) mVipUser {
	m.mVipUserDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mVipUser) replaceDB(db *gorm.DB) mVipUser {
	m.mVipUserDo.ReplaceDB(db)
	return m
}

type mVipUserDo struct{ gen.DO }

type IMVipUserDo interface {
	gen.SubQuery
	Debug() IMVipUserDo
	WithContext(ctx context.Context) IMVipUserDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMVipUserDo
	WriteDB() IMVipUserDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMVipUserDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMVipUserDo
	Not(conds ...gen.Condition) IMVipUserDo
	Or(conds ...gen.Condition) IMVipUserDo
	Select(conds ...field.Expr) IMVipUserDo
	Where(conds ...gen.Condition) IMVipUserDo
	Order(conds ...field.Expr) IMVipUserDo
	Distinct(cols ...field.Expr) IMVipUserDo
	Omit(cols ...field.Expr) IMVipUserDo
	Join(table schema.Tabler, on ...field.Expr) IMVipUserDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMVipUserDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMVipUserDo
	Group(cols ...field.Expr) IMVipUserDo
	Having(conds ...gen.Condition) IMVipUserDo
	Limit(limit int) IMVipUserDo
	Offset(offset int) IMVipUserDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMVipUserDo
	Unscoped() IMVipUserDo
	Create(values ...*model.MVipUser) error
	CreateInBatches(values []*model.MVipUser, batchSize int) error
	Save(values ...*model.MVipUser) error
	First() (*model.MVipUser, error)
	Take() (*model.MVipUser, error)
	Last() (*model.MVipUser, error)
	Find() ([]*model.MVipUser, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MVipUser, err error)
	FindInBatches(result *[]*model.MVipUser, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MVipUser) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMVipUserDo
	Assign(attrs ...field.AssignExpr) IMVipUserDo
	Joins(fields ...field.RelationField) IMVipUserDo
	Preload(fields ...field.RelationField) IMVipUserDo
	FirstOrInit() (*model.MVipUser, error)
	FirstOrCreate() (*model.MVipUser, error)
	FindByPage(offset int, limit int) (result []*model.MVipUser, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMVipUserDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mVipUserDo) Debug() IMVipUserDo {
	return m.withDO(m.DO.Debug())
}

func (m mVipUserDo) WithContext(ctx context.Context) IMVipUserDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mVipUserDo) ReadDB() IMVipUserDo {
	return m.Clauses(dbresolver.Read)
}

func (m mVipUserDo) WriteDB() IMVipUserDo {
	return m.Clauses(dbresolver.Write)
}

func (m mVipUserDo) Session(config *gorm.Session) IMVipUserDo {
	return m.withDO(m.DO.Session(config))
}

func (m mVipUserDo) Clauses(conds ...clause.Expression) IMVipUserDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mVipUserDo) Returning(value interface{}, columns ...string) IMVipUserDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mVipUserDo) Not(conds ...gen.Condition) IMVipUserDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mVipUserDo) Or(conds ...gen.Condition) IMVipUserDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mVipUserDo) Select(conds ...field.Expr) IMVipUserDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mVipUserDo) Where(conds ...gen.Condition) IMVipUserDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mVipUserDo) Order(conds ...field.Expr) IMVipUserDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mVipUserDo) Distinct(cols ...field.Expr) IMVipUserDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mVipUserDo) Omit(cols ...field.Expr) IMVipUserDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mVipUserDo) Join(table schema.Tabler, on ...field.Expr) IMVipUserDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mVipUserDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMVipUserDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mVipUserDo) RightJoin(table schema.Tabler, on ...field.Expr) IMVipUserDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mVipUserDo) Group(cols ...field.Expr) IMVipUserDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mVipUserDo) Having(conds ...gen.Condition) IMVipUserDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mVipUserDo) Limit(limit int) IMVipUserDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mVipUserDo) Offset(offset int) IMVipUserDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mVipUserDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMVipUserDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mVipUserDo) Unscoped() IMVipUserDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mVipUserDo) Create(values ...*model.MVipUser) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mVipUserDo) CreateInBatches(values []*model.MVipUser, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mVipUserDo) Save(values ...*model.MVipUser) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mVipUserDo) First() (*model.MVipUser, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MVipUser), nil
	}
}

func (m mVipUserDo) Take() (*model.MVipUser, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MVipUser), nil
	}
}

func (m mVipUserDo) Last() (*model.MVipUser, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MVipUser), nil
	}
}

func (m mVipUserDo) Find() ([]*model.MVipUser, error) {
	result, err := m.DO.Find()
	return result.([]*model.MVipUser), err
}

func (m mVipUserDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MVipUser, err error) {
	buf := make([]*model.MVipUser, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mVipUserDo) FindInBatches(result *[]*model.MVipUser, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mVipUserDo) Attrs(attrs ...field.AssignExpr) IMVipUserDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mVipUserDo) Assign(attrs ...field.AssignExpr) IMVipUserDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mVipUserDo) Joins(fields ...field.RelationField) IMVipUserDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mVipUserDo) Preload(fields ...field.RelationField) IMVipUserDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mVipUserDo) FirstOrInit() (*model.MVipUser, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MVipUser), nil
	}
}

func (m mVipUserDo) FirstOrCreate() (*model.MVipUser, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MVipUser), nil
	}
}

func (m mVipUserDo) FindByPage(offset int, limit int) (result []*model.MVipUser, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mVipUserDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mVipUserDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mVipUserDo) Delete(models ...*model.MVipUser) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mVipUserDo) withDO(do gen.Dao) *mVipUserDo {
	m.DO = *do.(*gen.DO)
	return m
}
