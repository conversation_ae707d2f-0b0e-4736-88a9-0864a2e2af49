// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMVipCustomerServiceBasic(db *gorm.DB, opts ...gen.DOOption) mVipCustomerServiceBasic {
	_mVipCustomerServiceBasic := mVipCustomerServiceBasic{}

	_mVipCustomerServiceBasic.mVipCustomerServiceBasicDo.UseDB(db, opts...)
	_mVipCustomerServiceBasic.mVipCustomerServiceBasicDo.UseModel(&model.MVipCustomerServiceBasic{})

	tableName := _mVipCustomerServiceBasic.mVipCustomerServiceBasicDo.TableName()
	_mVipCustomerServiceBasic.ALL = field.NewAsterisk(tableName)
	_mVipCustomerServiceBasic.ID = field.NewInt32(tableName, "id")
	_mVipCustomerServiceBasic.ServiceID = field.NewString(tableName, "service_id")
	_mVipCustomerServiceBasic.ServiceName = field.NewString(tableName, "service_name")
	_mVipCustomerServiceBasic.IsActive = field.NewBool(tableName, "is_active")
	_mVipCustomerServiceBasic.CreatedAt = field.NewInt64(tableName, "created_at")
	_mVipCustomerServiceBasic.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mVipCustomerServiceBasic.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mVipCustomerServiceBasic.fillFieldMap()

	return _mVipCustomerServiceBasic
}

type mVipCustomerServiceBasic struct {
	mVipCustomerServiceBasicDo

	ALL         field.Asterisk
	ID          field.Int32
	ServiceID   field.String // 客服ID
	ServiceName field.String // 客服名称
	IsActive    field.Bool   // 是否激活
	CreatedAt   field.Int64
	UpdatedAt   field.Int64
	IsDeleted   field.Bool

	fieldMap map[string]field.Expr
}

func (m mVipCustomerServiceBasic) Table(newTableName string) *mVipCustomerServiceBasic {
	m.mVipCustomerServiceBasicDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mVipCustomerServiceBasic) As(alias string) *mVipCustomerServiceBasic {
	m.mVipCustomerServiceBasicDo.DO = *(m.mVipCustomerServiceBasicDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mVipCustomerServiceBasic) updateTableName(table string) *mVipCustomerServiceBasic {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.ServiceID = field.NewString(table, "service_id")
	m.ServiceName = field.NewString(table, "service_name")
	m.IsActive = field.NewBool(table, "is_active")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mVipCustomerServiceBasic) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mVipCustomerServiceBasic) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 7)
	m.fieldMap["id"] = m.ID
	m.fieldMap["service_id"] = m.ServiceID
	m.fieldMap["service_name"] = m.ServiceName
	m.fieldMap["is_active"] = m.IsActive
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mVipCustomerServiceBasic) clone(db *gorm.DB) mVipCustomerServiceBasic {
	m.mVipCustomerServiceBasicDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mVipCustomerServiceBasic) replaceDB(db *gorm.DB) mVipCustomerServiceBasic {
	m.mVipCustomerServiceBasicDo.ReplaceDB(db)
	return m
}

type mVipCustomerServiceBasicDo struct{ gen.DO }

type IMVipCustomerServiceBasicDo interface {
	gen.SubQuery
	Debug() IMVipCustomerServiceBasicDo
	WithContext(ctx context.Context) IMVipCustomerServiceBasicDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMVipCustomerServiceBasicDo
	WriteDB() IMVipCustomerServiceBasicDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMVipCustomerServiceBasicDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMVipCustomerServiceBasicDo
	Not(conds ...gen.Condition) IMVipCustomerServiceBasicDo
	Or(conds ...gen.Condition) IMVipCustomerServiceBasicDo
	Select(conds ...field.Expr) IMVipCustomerServiceBasicDo
	Where(conds ...gen.Condition) IMVipCustomerServiceBasicDo
	Order(conds ...field.Expr) IMVipCustomerServiceBasicDo
	Distinct(cols ...field.Expr) IMVipCustomerServiceBasicDo
	Omit(cols ...field.Expr) IMVipCustomerServiceBasicDo
	Join(table schema.Tabler, on ...field.Expr) IMVipCustomerServiceBasicDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMVipCustomerServiceBasicDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMVipCustomerServiceBasicDo
	Group(cols ...field.Expr) IMVipCustomerServiceBasicDo
	Having(conds ...gen.Condition) IMVipCustomerServiceBasicDo
	Limit(limit int) IMVipCustomerServiceBasicDo
	Offset(offset int) IMVipCustomerServiceBasicDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMVipCustomerServiceBasicDo
	Unscoped() IMVipCustomerServiceBasicDo
	Create(values ...*model.MVipCustomerServiceBasic) error
	CreateInBatches(values []*model.MVipCustomerServiceBasic, batchSize int) error
	Save(values ...*model.MVipCustomerServiceBasic) error
	First() (*model.MVipCustomerServiceBasic, error)
	Take() (*model.MVipCustomerServiceBasic, error)
	Last() (*model.MVipCustomerServiceBasic, error)
	Find() ([]*model.MVipCustomerServiceBasic, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MVipCustomerServiceBasic, err error)
	FindInBatches(result *[]*model.MVipCustomerServiceBasic, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MVipCustomerServiceBasic) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMVipCustomerServiceBasicDo
	Assign(attrs ...field.AssignExpr) IMVipCustomerServiceBasicDo
	Joins(fields ...field.RelationField) IMVipCustomerServiceBasicDo
	Preload(fields ...field.RelationField) IMVipCustomerServiceBasicDo
	FirstOrInit() (*model.MVipCustomerServiceBasic, error)
	FirstOrCreate() (*model.MVipCustomerServiceBasic, error)
	FindByPage(offset int, limit int) (result []*model.MVipCustomerServiceBasic, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMVipCustomerServiceBasicDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mVipCustomerServiceBasicDo) Debug() IMVipCustomerServiceBasicDo {
	return m.withDO(m.DO.Debug())
}

func (m mVipCustomerServiceBasicDo) WithContext(ctx context.Context) IMVipCustomerServiceBasicDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mVipCustomerServiceBasicDo) ReadDB() IMVipCustomerServiceBasicDo {
	return m.Clauses(dbresolver.Read)
}

func (m mVipCustomerServiceBasicDo) WriteDB() IMVipCustomerServiceBasicDo {
	return m.Clauses(dbresolver.Write)
}

func (m mVipCustomerServiceBasicDo) Session(config *gorm.Session) IMVipCustomerServiceBasicDo {
	return m.withDO(m.DO.Session(config))
}

func (m mVipCustomerServiceBasicDo) Clauses(conds ...clause.Expression) IMVipCustomerServiceBasicDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mVipCustomerServiceBasicDo) Returning(value interface{}, columns ...string) IMVipCustomerServiceBasicDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mVipCustomerServiceBasicDo) Not(conds ...gen.Condition) IMVipCustomerServiceBasicDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mVipCustomerServiceBasicDo) Or(conds ...gen.Condition) IMVipCustomerServiceBasicDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mVipCustomerServiceBasicDo) Select(conds ...field.Expr) IMVipCustomerServiceBasicDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mVipCustomerServiceBasicDo) Where(conds ...gen.Condition) IMVipCustomerServiceBasicDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mVipCustomerServiceBasicDo) Order(conds ...field.Expr) IMVipCustomerServiceBasicDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mVipCustomerServiceBasicDo) Distinct(cols ...field.Expr) IMVipCustomerServiceBasicDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mVipCustomerServiceBasicDo) Omit(cols ...field.Expr) IMVipCustomerServiceBasicDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mVipCustomerServiceBasicDo) Join(table schema.Tabler, on ...field.Expr) IMVipCustomerServiceBasicDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mVipCustomerServiceBasicDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMVipCustomerServiceBasicDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mVipCustomerServiceBasicDo) RightJoin(table schema.Tabler, on ...field.Expr) IMVipCustomerServiceBasicDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mVipCustomerServiceBasicDo) Group(cols ...field.Expr) IMVipCustomerServiceBasicDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mVipCustomerServiceBasicDo) Having(conds ...gen.Condition) IMVipCustomerServiceBasicDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mVipCustomerServiceBasicDo) Limit(limit int) IMVipCustomerServiceBasicDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mVipCustomerServiceBasicDo) Offset(offset int) IMVipCustomerServiceBasicDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mVipCustomerServiceBasicDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMVipCustomerServiceBasicDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mVipCustomerServiceBasicDo) Unscoped() IMVipCustomerServiceBasicDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mVipCustomerServiceBasicDo) Create(values ...*model.MVipCustomerServiceBasic) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mVipCustomerServiceBasicDo) CreateInBatches(values []*model.MVipCustomerServiceBasic, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mVipCustomerServiceBasicDo) Save(values ...*model.MVipCustomerServiceBasic) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mVipCustomerServiceBasicDo) First() (*model.MVipCustomerServiceBasic, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MVipCustomerServiceBasic), nil
	}
}

func (m mVipCustomerServiceBasicDo) Take() (*model.MVipCustomerServiceBasic, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MVipCustomerServiceBasic), nil
	}
}

func (m mVipCustomerServiceBasicDo) Last() (*model.MVipCustomerServiceBasic, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MVipCustomerServiceBasic), nil
	}
}

func (m mVipCustomerServiceBasicDo) Find() ([]*model.MVipCustomerServiceBasic, error) {
	result, err := m.DO.Find()
	return result.([]*model.MVipCustomerServiceBasic), err
}

func (m mVipCustomerServiceBasicDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MVipCustomerServiceBasic, err error) {
	buf := make([]*model.MVipCustomerServiceBasic, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mVipCustomerServiceBasicDo) FindInBatches(result *[]*model.MVipCustomerServiceBasic, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mVipCustomerServiceBasicDo) Attrs(attrs ...field.AssignExpr) IMVipCustomerServiceBasicDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mVipCustomerServiceBasicDo) Assign(attrs ...field.AssignExpr) IMVipCustomerServiceBasicDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mVipCustomerServiceBasicDo) Joins(fields ...field.RelationField) IMVipCustomerServiceBasicDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mVipCustomerServiceBasicDo) Preload(fields ...field.RelationField) IMVipCustomerServiceBasicDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mVipCustomerServiceBasicDo) FirstOrInit() (*model.MVipCustomerServiceBasic, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MVipCustomerServiceBasic), nil
	}
}

func (m mVipCustomerServiceBasicDo) FirstOrCreate() (*model.MVipCustomerServiceBasic, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MVipCustomerServiceBasic), nil
	}
}

func (m mVipCustomerServiceBasicDo) FindByPage(offset int, limit int) (result []*model.MVipCustomerServiceBasic, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mVipCustomerServiceBasicDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mVipCustomerServiceBasicDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mVipCustomerServiceBasicDo) Delete(models ...*model.MVipCustomerServiceBasic) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mVipCustomerServiceBasicDo) withDO(do gen.Dao) *mVipCustomerServiceBasicDo {
	m.DO = *do.(*gen.DO)
	return m
}
