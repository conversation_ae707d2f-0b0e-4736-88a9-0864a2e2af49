// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMCustomerServiceBasic(db *gorm.DB, opts ...gen.DOOption) mCustomerServiceBasic {
	_mCustomerServiceBasic := mCustomerServiceBasic{}

	_mCustomerServiceBasic.mCustomerServiceBasicDo.UseDB(db, opts...)
	_mCustomerServiceBasic.mCustomerServiceBasicDo.UseModel(&model.MCustomerServiceBasic{})

	tableName := _mCustomerServiceBasic.mCustomerServiceBasicDo.TableName()
	_mCustomerServiceBasic.ALL = field.NewAsterisk(tableName)
	_mCustomerServiceBasic.ID = field.NewInt32(tableName, "id")
	_mCustomerServiceBasic.ServiceID = field.NewString(tableName, "service_id")
	_mCustomerServiceBasic.ServiceName = field.NewString(tableName, "service_name")
	_mCustomerServiceBasic.IsActive = field.NewBool(tableName, "is_active")
	_mCustomerServiceBasic.CreatedAt = field.NewInt64(tableName, "created_at")
	_mCustomerServiceBasic.UpdatedAt = field.NewInt64(tableName, "updated_at")
	_mCustomerServiceBasic.IsDeleted = field.NewBool(tableName, "is_deleted")

	_mCustomerServiceBasic.fillFieldMap()

	return _mCustomerServiceBasic
}

type mCustomerServiceBasic struct {
	mCustomerServiceBasicDo

	ALL         field.Asterisk
	ID          field.Int32
	ServiceID   field.String // 客服ID
	ServiceName field.String // 客服名称
	IsActive    field.Bool   // 是否激活
	CreatedAt   field.Int64
	UpdatedAt   field.Int64
	IsDeleted   field.Bool

	fieldMap map[string]field.Expr
}

func (m mCustomerServiceBasic) Table(newTableName string) *mCustomerServiceBasic {
	m.mCustomerServiceBasicDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mCustomerServiceBasic) As(alias string) *mCustomerServiceBasic {
	m.mCustomerServiceBasicDo.DO = *(m.mCustomerServiceBasicDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mCustomerServiceBasic) updateTableName(table string) *mCustomerServiceBasic {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt32(table, "id")
	m.ServiceID = field.NewString(table, "service_id")
	m.ServiceName = field.NewString(table, "service_name")
	m.IsActive = field.NewBool(table, "is_active")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")
	m.IsDeleted = field.NewBool(table, "is_deleted")

	m.fillFieldMap()

	return m
}

func (m *mCustomerServiceBasic) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mCustomerServiceBasic) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 7)
	m.fieldMap["id"] = m.ID
	m.fieldMap["service_id"] = m.ServiceID
	m.fieldMap["service_name"] = m.ServiceName
	m.fieldMap["is_active"] = m.IsActive
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
	m.fieldMap["is_deleted"] = m.IsDeleted
}

func (m mCustomerServiceBasic) clone(db *gorm.DB) mCustomerServiceBasic {
	m.mCustomerServiceBasicDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mCustomerServiceBasic) replaceDB(db *gorm.DB) mCustomerServiceBasic {
	m.mCustomerServiceBasicDo.ReplaceDB(db)
	return m
}

type mCustomerServiceBasicDo struct{ gen.DO }

type IMCustomerServiceBasicDo interface {
	gen.SubQuery
	Debug() IMCustomerServiceBasicDo
	WithContext(ctx context.Context) IMCustomerServiceBasicDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMCustomerServiceBasicDo
	WriteDB() IMCustomerServiceBasicDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMCustomerServiceBasicDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMCustomerServiceBasicDo
	Not(conds ...gen.Condition) IMCustomerServiceBasicDo
	Or(conds ...gen.Condition) IMCustomerServiceBasicDo
	Select(conds ...field.Expr) IMCustomerServiceBasicDo
	Where(conds ...gen.Condition) IMCustomerServiceBasicDo
	Order(conds ...field.Expr) IMCustomerServiceBasicDo
	Distinct(cols ...field.Expr) IMCustomerServiceBasicDo
	Omit(cols ...field.Expr) IMCustomerServiceBasicDo
	Join(table schema.Tabler, on ...field.Expr) IMCustomerServiceBasicDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMCustomerServiceBasicDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMCustomerServiceBasicDo
	Group(cols ...field.Expr) IMCustomerServiceBasicDo
	Having(conds ...gen.Condition) IMCustomerServiceBasicDo
	Limit(limit int) IMCustomerServiceBasicDo
	Offset(offset int) IMCustomerServiceBasicDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMCustomerServiceBasicDo
	Unscoped() IMCustomerServiceBasicDo
	Create(values ...*model.MCustomerServiceBasic) error
	CreateInBatches(values []*model.MCustomerServiceBasic, batchSize int) error
	Save(values ...*model.MCustomerServiceBasic) error
	First() (*model.MCustomerServiceBasic, error)
	Take() (*model.MCustomerServiceBasic, error)
	Last() (*model.MCustomerServiceBasic, error)
	Find() ([]*model.MCustomerServiceBasic, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MCustomerServiceBasic, err error)
	FindInBatches(result *[]*model.MCustomerServiceBasic, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MCustomerServiceBasic) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMCustomerServiceBasicDo
	Assign(attrs ...field.AssignExpr) IMCustomerServiceBasicDo
	Joins(fields ...field.RelationField) IMCustomerServiceBasicDo
	Preload(fields ...field.RelationField) IMCustomerServiceBasicDo
	FirstOrInit() (*model.MCustomerServiceBasic, error)
	FirstOrCreate() (*model.MCustomerServiceBasic, error)
	FindByPage(offset int, limit int) (result []*model.MCustomerServiceBasic, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMCustomerServiceBasicDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mCustomerServiceBasicDo) Debug() IMCustomerServiceBasicDo {
	return m.withDO(m.DO.Debug())
}

func (m mCustomerServiceBasicDo) WithContext(ctx context.Context) IMCustomerServiceBasicDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mCustomerServiceBasicDo) ReadDB() IMCustomerServiceBasicDo {
	return m.Clauses(dbresolver.Read)
}

func (m mCustomerServiceBasicDo) WriteDB() IMCustomerServiceBasicDo {
	return m.Clauses(dbresolver.Write)
}

func (m mCustomerServiceBasicDo) Session(config *gorm.Session) IMCustomerServiceBasicDo {
	return m.withDO(m.DO.Session(config))
}

func (m mCustomerServiceBasicDo) Clauses(conds ...clause.Expression) IMCustomerServiceBasicDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mCustomerServiceBasicDo) Returning(value interface{}, columns ...string) IMCustomerServiceBasicDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mCustomerServiceBasicDo) Not(conds ...gen.Condition) IMCustomerServiceBasicDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mCustomerServiceBasicDo) Or(conds ...gen.Condition) IMCustomerServiceBasicDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mCustomerServiceBasicDo) Select(conds ...field.Expr) IMCustomerServiceBasicDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mCustomerServiceBasicDo) Where(conds ...gen.Condition) IMCustomerServiceBasicDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mCustomerServiceBasicDo) Order(conds ...field.Expr) IMCustomerServiceBasicDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mCustomerServiceBasicDo) Distinct(cols ...field.Expr) IMCustomerServiceBasicDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mCustomerServiceBasicDo) Omit(cols ...field.Expr) IMCustomerServiceBasicDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mCustomerServiceBasicDo) Join(table schema.Tabler, on ...field.Expr) IMCustomerServiceBasicDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mCustomerServiceBasicDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMCustomerServiceBasicDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mCustomerServiceBasicDo) RightJoin(table schema.Tabler, on ...field.Expr) IMCustomerServiceBasicDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mCustomerServiceBasicDo) Group(cols ...field.Expr) IMCustomerServiceBasicDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mCustomerServiceBasicDo) Having(conds ...gen.Condition) IMCustomerServiceBasicDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mCustomerServiceBasicDo) Limit(limit int) IMCustomerServiceBasicDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mCustomerServiceBasicDo) Offset(offset int) IMCustomerServiceBasicDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mCustomerServiceBasicDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMCustomerServiceBasicDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mCustomerServiceBasicDo) Unscoped() IMCustomerServiceBasicDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mCustomerServiceBasicDo) Create(values ...*model.MCustomerServiceBasic) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mCustomerServiceBasicDo) CreateInBatches(values []*model.MCustomerServiceBasic, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mCustomerServiceBasicDo) Save(values ...*model.MCustomerServiceBasic) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mCustomerServiceBasicDo) First() (*model.MCustomerServiceBasic, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCustomerServiceBasic), nil
	}
}

func (m mCustomerServiceBasicDo) Take() (*model.MCustomerServiceBasic, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCustomerServiceBasic), nil
	}
}

func (m mCustomerServiceBasicDo) Last() (*model.MCustomerServiceBasic, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCustomerServiceBasic), nil
	}
}

func (m mCustomerServiceBasicDo) Find() ([]*model.MCustomerServiceBasic, error) {
	result, err := m.DO.Find()
	return result.([]*model.MCustomerServiceBasic), err
}

func (m mCustomerServiceBasicDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MCustomerServiceBasic, err error) {
	buf := make([]*model.MCustomerServiceBasic, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mCustomerServiceBasicDo) FindInBatches(result *[]*model.MCustomerServiceBasic, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mCustomerServiceBasicDo) Attrs(attrs ...field.AssignExpr) IMCustomerServiceBasicDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mCustomerServiceBasicDo) Assign(attrs ...field.AssignExpr) IMCustomerServiceBasicDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mCustomerServiceBasicDo) Joins(fields ...field.RelationField) IMCustomerServiceBasicDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mCustomerServiceBasicDo) Preload(fields ...field.RelationField) IMCustomerServiceBasicDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mCustomerServiceBasicDo) FirstOrInit() (*model.MCustomerServiceBasic, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCustomerServiceBasic), nil
	}
}

func (m mCustomerServiceBasicDo) FirstOrCreate() (*model.MCustomerServiceBasic, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MCustomerServiceBasic), nil
	}
}

func (m mCustomerServiceBasicDo) FindByPage(offset int, limit int) (result []*model.MCustomerServiceBasic, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mCustomerServiceBasicDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mCustomerServiceBasicDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mCustomerServiceBasicDo) Delete(models ...*model.MCustomerServiceBasic) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mCustomerServiceBasicDo) withDO(do gen.Dao) *mCustomerServiceBasicDo {
	m.DO = *do.(*gen.DO)
	return m
}
