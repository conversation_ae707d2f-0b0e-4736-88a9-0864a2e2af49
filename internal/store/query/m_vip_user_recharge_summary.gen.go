// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
)

func newMVipUserRechargeSummary(db *gorm.DB, opts ...gen.DOOption) mVipUserRechargeSummary {
	_mVipUserRechargeSummary := mVipUserRechargeSummary{}

	_mVipUserRechargeSummary.mVipUserRechargeSummaryDo.UseDB(db, opts...)
	_mVipUserRechargeSummary.mVipUserRechargeSummaryDo.UseModel(&model.MVipUserRechargeSummary{})

	tableName := _mVipUserRechargeSummary.mVipUserRechargeSummaryDo.TableName()
	_mVipUserRechargeSummary.ALL = field.NewAsterisk(tableName)
	_mVipUserRechargeSummary.ID = field.NewInt64(tableName, "id")
	_mVipUserRechargeSummary.UserID = field.NewString(tableName, "user_id")
	_mVipUserRechargeSummary.GameID = field.NewString(tableName, "game_id")
	_mVipUserRechargeSummary.PlatformType = field.NewString(tableName, "platform_type")
	_mVipUserRechargeSummary.TotalAmount = field.NewInt64(tableName, "total_amount")
	_mVipUserRechargeSummary.FirstRechargeAt = field.NewInt64(tableName, "first_recharge_at")
	_mVipUserRechargeSummary.LastRechargeAt = field.NewInt64(tableName, "last_recharge_at")
	_mVipUserRechargeSummary.LastSyncAt = field.NewInt64(tableName, "last_sync_at")
	_mVipUserRechargeSummary.CreatedAt = field.NewInt64(tableName, "created_at")
	_mVipUserRechargeSummary.UpdatedAt = field.NewInt64(tableName, "updated_at")

	_mVipUserRechargeSummary.fillFieldMap()

	return _mVipUserRechargeSummary
}

type mVipUserRechargeSummary struct {
	mVipUserRechargeSummaryDo

	ALL             field.Asterisk
	ID              field.Int64
	UserID          field.String // 用户ID
	GameID          field.String // 游戏ID
	PlatformType    field.String // 平台类型
	TotalAmount     field.Int64  // 累计充值金额(分)
	FirstRechargeAt field.Int64  // 首次充值时间
	LastRechargeAt  field.Int64  // 最后充值时间
	LastSyncAt      field.Int64  // 最后同步时间
	CreatedAt       field.Int64
	UpdatedAt       field.Int64

	fieldMap map[string]field.Expr
}

func (m mVipUserRechargeSummary) Table(newTableName string) *mVipUserRechargeSummary {
	m.mVipUserRechargeSummaryDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m mVipUserRechargeSummary) As(alias string) *mVipUserRechargeSummary {
	m.mVipUserRechargeSummaryDo.DO = *(m.mVipUserRechargeSummaryDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *mVipUserRechargeSummary) updateTableName(table string) *mVipUserRechargeSummary {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt64(table, "id")
	m.UserID = field.NewString(table, "user_id")
	m.GameID = field.NewString(table, "game_id")
	m.PlatformType = field.NewString(table, "platform_type")
	m.TotalAmount = field.NewInt64(table, "total_amount")
	m.FirstRechargeAt = field.NewInt64(table, "first_recharge_at")
	m.LastRechargeAt = field.NewInt64(table, "last_recharge_at")
	m.LastSyncAt = field.NewInt64(table, "last_sync_at")
	m.CreatedAt = field.NewInt64(table, "created_at")
	m.UpdatedAt = field.NewInt64(table, "updated_at")

	m.fillFieldMap()

	return m
}

func (m *mVipUserRechargeSummary) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *mVipUserRechargeSummary) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 10)
	m.fieldMap["id"] = m.ID
	m.fieldMap["user_id"] = m.UserID
	m.fieldMap["game_id"] = m.GameID
	m.fieldMap["platform_type"] = m.PlatformType
	m.fieldMap["total_amount"] = m.TotalAmount
	m.fieldMap["first_recharge_at"] = m.FirstRechargeAt
	m.fieldMap["last_recharge_at"] = m.LastRechargeAt
	m.fieldMap["last_sync_at"] = m.LastSyncAt
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
}

func (m mVipUserRechargeSummary) clone(db *gorm.DB) mVipUserRechargeSummary {
	m.mVipUserRechargeSummaryDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m mVipUserRechargeSummary) replaceDB(db *gorm.DB) mVipUserRechargeSummary {
	m.mVipUserRechargeSummaryDo.ReplaceDB(db)
	return m
}

type mVipUserRechargeSummaryDo struct{ gen.DO }

type IMVipUserRechargeSummaryDo interface {
	gen.SubQuery
	Debug() IMVipUserRechargeSummaryDo
	WithContext(ctx context.Context) IMVipUserRechargeSummaryDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMVipUserRechargeSummaryDo
	WriteDB() IMVipUserRechargeSummaryDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMVipUserRechargeSummaryDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMVipUserRechargeSummaryDo
	Not(conds ...gen.Condition) IMVipUserRechargeSummaryDo
	Or(conds ...gen.Condition) IMVipUserRechargeSummaryDo
	Select(conds ...field.Expr) IMVipUserRechargeSummaryDo
	Where(conds ...gen.Condition) IMVipUserRechargeSummaryDo
	Order(conds ...field.Expr) IMVipUserRechargeSummaryDo
	Distinct(cols ...field.Expr) IMVipUserRechargeSummaryDo
	Omit(cols ...field.Expr) IMVipUserRechargeSummaryDo
	Join(table schema.Tabler, on ...field.Expr) IMVipUserRechargeSummaryDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMVipUserRechargeSummaryDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMVipUserRechargeSummaryDo
	Group(cols ...field.Expr) IMVipUserRechargeSummaryDo
	Having(conds ...gen.Condition) IMVipUserRechargeSummaryDo
	Limit(limit int) IMVipUserRechargeSummaryDo
	Offset(offset int) IMVipUserRechargeSummaryDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMVipUserRechargeSummaryDo
	Unscoped() IMVipUserRechargeSummaryDo
	Create(values ...*model.MVipUserRechargeSummary) error
	CreateInBatches(values []*model.MVipUserRechargeSummary, batchSize int) error
	Save(values ...*model.MVipUserRechargeSummary) error
	First() (*model.MVipUserRechargeSummary, error)
	Take() (*model.MVipUserRechargeSummary, error)
	Last() (*model.MVipUserRechargeSummary, error)
	Find() ([]*model.MVipUserRechargeSummary, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MVipUserRechargeSummary, err error)
	FindInBatches(result *[]*model.MVipUserRechargeSummary, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MVipUserRechargeSummary) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMVipUserRechargeSummaryDo
	Assign(attrs ...field.AssignExpr) IMVipUserRechargeSummaryDo
	Joins(fields ...field.RelationField) IMVipUserRechargeSummaryDo
	Preload(fields ...field.RelationField) IMVipUserRechargeSummaryDo
	FirstOrInit() (*model.MVipUserRechargeSummary, error)
	FirstOrCreate() (*model.MVipUserRechargeSummary, error)
	FindByPage(offset int, limit int) (result []*model.MVipUserRechargeSummary, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMVipUserRechargeSummaryDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m mVipUserRechargeSummaryDo) Debug() IMVipUserRechargeSummaryDo {
	return m.withDO(m.DO.Debug())
}

func (m mVipUserRechargeSummaryDo) WithContext(ctx context.Context) IMVipUserRechargeSummaryDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m mVipUserRechargeSummaryDo) ReadDB() IMVipUserRechargeSummaryDo {
	return m.Clauses(dbresolver.Read)
}

func (m mVipUserRechargeSummaryDo) WriteDB() IMVipUserRechargeSummaryDo {
	return m.Clauses(dbresolver.Write)
}

func (m mVipUserRechargeSummaryDo) Session(config *gorm.Session) IMVipUserRechargeSummaryDo {
	return m.withDO(m.DO.Session(config))
}

func (m mVipUserRechargeSummaryDo) Clauses(conds ...clause.Expression) IMVipUserRechargeSummaryDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m mVipUserRechargeSummaryDo) Returning(value interface{}, columns ...string) IMVipUserRechargeSummaryDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m mVipUserRechargeSummaryDo) Not(conds ...gen.Condition) IMVipUserRechargeSummaryDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m mVipUserRechargeSummaryDo) Or(conds ...gen.Condition) IMVipUserRechargeSummaryDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m mVipUserRechargeSummaryDo) Select(conds ...field.Expr) IMVipUserRechargeSummaryDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m mVipUserRechargeSummaryDo) Where(conds ...gen.Condition) IMVipUserRechargeSummaryDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m mVipUserRechargeSummaryDo) Order(conds ...field.Expr) IMVipUserRechargeSummaryDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m mVipUserRechargeSummaryDo) Distinct(cols ...field.Expr) IMVipUserRechargeSummaryDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m mVipUserRechargeSummaryDo) Omit(cols ...field.Expr) IMVipUserRechargeSummaryDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m mVipUserRechargeSummaryDo) Join(table schema.Tabler, on ...field.Expr) IMVipUserRechargeSummaryDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m mVipUserRechargeSummaryDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMVipUserRechargeSummaryDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m mVipUserRechargeSummaryDo) RightJoin(table schema.Tabler, on ...field.Expr) IMVipUserRechargeSummaryDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m mVipUserRechargeSummaryDo) Group(cols ...field.Expr) IMVipUserRechargeSummaryDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m mVipUserRechargeSummaryDo) Having(conds ...gen.Condition) IMVipUserRechargeSummaryDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m mVipUserRechargeSummaryDo) Limit(limit int) IMVipUserRechargeSummaryDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m mVipUserRechargeSummaryDo) Offset(offset int) IMVipUserRechargeSummaryDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m mVipUserRechargeSummaryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMVipUserRechargeSummaryDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m mVipUserRechargeSummaryDo) Unscoped() IMVipUserRechargeSummaryDo {
	return m.withDO(m.DO.Unscoped())
}

func (m mVipUserRechargeSummaryDo) Create(values ...*model.MVipUserRechargeSummary) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m mVipUserRechargeSummaryDo) CreateInBatches(values []*model.MVipUserRechargeSummary, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m mVipUserRechargeSummaryDo) Save(values ...*model.MVipUserRechargeSummary) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m mVipUserRechargeSummaryDo) First() (*model.MVipUserRechargeSummary, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MVipUserRechargeSummary), nil
	}
}

func (m mVipUserRechargeSummaryDo) Take() (*model.MVipUserRechargeSummary, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MVipUserRechargeSummary), nil
	}
}

func (m mVipUserRechargeSummaryDo) Last() (*model.MVipUserRechargeSummary, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MVipUserRechargeSummary), nil
	}
}

func (m mVipUserRechargeSummaryDo) Find() ([]*model.MVipUserRechargeSummary, error) {
	result, err := m.DO.Find()
	return result.([]*model.MVipUserRechargeSummary), err
}

func (m mVipUserRechargeSummaryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MVipUserRechargeSummary, err error) {
	buf := make([]*model.MVipUserRechargeSummary, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m mVipUserRechargeSummaryDo) FindInBatches(result *[]*model.MVipUserRechargeSummary, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m mVipUserRechargeSummaryDo) Attrs(attrs ...field.AssignExpr) IMVipUserRechargeSummaryDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m mVipUserRechargeSummaryDo) Assign(attrs ...field.AssignExpr) IMVipUserRechargeSummaryDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m mVipUserRechargeSummaryDo) Joins(fields ...field.RelationField) IMVipUserRechargeSummaryDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m mVipUserRechargeSummaryDo) Preload(fields ...field.RelationField) IMVipUserRechargeSummaryDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m mVipUserRechargeSummaryDo) FirstOrInit() (*model.MVipUserRechargeSummary, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MVipUserRechargeSummary), nil
	}
}

func (m mVipUserRechargeSummaryDo) FirstOrCreate() (*model.MVipUserRechargeSummary, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MVipUserRechargeSummary), nil
	}
}

func (m mVipUserRechargeSummaryDo) FindByPage(offset int, limit int) (result []*model.MVipUserRechargeSummary, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m mVipUserRechargeSummaryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m mVipUserRechargeSummaryDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m mVipUserRechargeSummaryDo) Delete(models ...*model.MVipUserRechargeSummary) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *mVipUserRechargeSummaryDo) withDO(do gen.Dao) *mVipUserRechargeSummaryDo {
	m.DO = *do.(*gen.DO)
	return m
}
