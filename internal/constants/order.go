package constants

import "time"

// 设备ID相关常量
const (
	// DeviceIDStreamerProxyPaymentProhibited 主播代理操作支付禁用设备ID标识
	DeviceIDStreamerProxyPaymentProhibited = "streamer_proxy_operation_payment_prohibited"
)

var PlatformTypeMap = map[int32]string{
	1: "ios",
	2: "android",
}

const (
	PlatformTypeIOS     = 1
	PlatformTypeAndroid = 2
)

// pay type
const (
	PayTypeAndroidWechatPay = 1
	PayTypeIOSWechatH5Pay   = 2
	PayTypeAndroidGooglePay = 3
	PayTypeIOSApplePay      = 4
	PayTypeAndroidDouyinPay = 5
	PayTypeIOSDouyinPay     = 6
	// Alipay
	PayTypeAndroidAlipay = 7
	PayTypeIOSAlipay     = 8
)

// order
const (
	PaymentOrderCreate            = 1 // 创建订单
	PaymentOrderWait              = 2 // 待支付
	PaymentWechatPaySuccess       = 3 // 微信｜抖音回调成功
	PaymentProductShipmentSuccess = 4 // 产品发货成功
	PaymentProductShipmentFail    = 5 // 产品发货失败
)

// pay
const (
	PayChannelTypeIOS           = "h5"
	PayChannelTypeAndroid       = "mds"
	PayChannelTypeDouyinIOS     = "douyin_ios"
	PayChannelTypeDouyinAndroid = "douyin_android"
	PayChannelTypeAlipayAndroid = "alipay_android"

	PayTypeCurrencyCNY = "CNY"

	// cz
	PayTypeCZ = "cz"
	// 充值
	PayTypeRecharge = "充值"

	// 客服消息文案
	CustomerServiceTitle = "点击我进行充值"
	CustomerServiceDesc  = "充值后返回游戏查看"
)

// 回调产品方最大重试次数
const (
	CallProductShipmentMaxCount = 8
	CallProductShipmentMinute   = 1

	ProductShipmentListGetNumber = 5 // 一次性从redis队列取出的个数
)

const (
	IOSPayPagePath = "index?bkx_order_id="
	ThumbURL       = "https://platform-client.bkxgame.com/platform_pay/pay_ico.png"
)

// DouyinPriceLevels 抖音价格规则
var (
	// DouyinPriceLevels = []int32{1, 3, 6, 8, 12, 18, 25, 30, 40, 45, 50, 60, 68, 73, 78, 88, 98, 108, 118, 128, 148, 168, 188, 198, 328, 648, 998, 1288, 1998, 2998}
	DouyinPriceLevels = []int32{100, 300, 600, 800, 1200, 1800, 2500, 3000, 4000, 4500, 5000, 6000, 6800, 7300, 7800, 8800, 9800, 10800, 11800, 12800, 14800, 16800, 18800, 19800, 32800, 64800, 99800, 128800, 199800, 299800}
)

// AlipayPriceLevels 支付宝限定价格等级（单位：分）
// 依据支付宝文档（单位：元）换算为分；用于控价校验
var AlipayPriceLevels = []int32{
	100, 300, 400, 600, 800, 900,
	1000, 1200, 1400, 1800, 2000,
	2500, 2800, 3000, 3400, 3500, 3600, 3800,
	4000, 4500, 5000, 5400, 6000, 6800, 7300,
	// 第一列（元）→ 分
	7800, 8800, 9000, 9300, 9800, 9900, 10000, 10800, 11000, 12000, 12800, 13500,
	13600, 13800, 14800, 15000, 15300, 15800, 16800, 18000, 18800, 19800, 20000,
	20400, 21000, 22800,
	// 第二列（元）→ 分
	24000, 25600, 27000, 27200, 30000, 32800, 34000, 34800, 38400, 40000, 51200,
	60000, 61800, 64000, 64800, 65600, 76800, 89600, 98400, 99800, 100000, 125600,
	128800, 129600,
	// 第三列（元）→ 分
	130000, 131200, 139800, 144000, 156800, 164000, 169600, 176800, 189600,
	194400, 196800, 199800, 200000, 212800, 225600, 229600, 238400, 242400, 255200,
	259200, 262400, 275200, 288000, 295200, 299800,
}

// RetryDelays 回调定时规则 将时间的重试机制改为  10s/30s/1m/2m/3m/4m/5m/6m/7m/8m/9m/10m/20m/30m/1h/2h - 总共4小时45m。
var RetryDelays = []time.Duration{
	10 * time.Second,
	30 * time.Second,
	1 * time.Minute,
	2 * time.Minute,
	3 * time.Minute,
	4 * time.Minute,
	5 * time.Minute,
	6 * time.Minute,
	7 * time.Minute,
	8 * time.Minute,
	9 * time.Minute,
	10 * time.Minute,
	20 * time.Minute,
	30 * time.Minute,
	1 * time.Hour,
	2 * time.Hour,
}
