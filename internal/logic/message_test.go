package logic

import (
	"testing"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"github.com/stretchr/testify/assert"
)

// TestSubMessageNotify_UserIDMapping 测试用户ID映射功能
func TestSubMessageNotify_UserIDMapping(t *testing.T) {
	// 简单初始化，设置一个测试用的映射配置
	config.GlobConfig.UserIDMapping = config.UserIDMappingConf{
		Enabled:   true,
		NewGameID: "test_game_123",
		OldGameID: "mapped_game_456",
	}

	messageLogic := &MessageLogic{}

	tests := []struct {
		name           string
		gameID         string
		userID         string
		unionID        string
		expectedResult bool
		description    string
	}{
		{
			name:           "配置游戏ID映射测试",
			gameID:         config.GlobConfig.UserIDMapping.NewGameID, // 使用配置的新游戏ID
			userID:         "test_user_hlxq_123",
			unionID:        "test_union_id_123",
			expectedResult: true,
			description:    "测试当game_id为配置的新游戏ID时，用户ID映射功能是否正常工作",
		},
		{
			name:           "非配置游戏ID跳过映射测试",
			gameID:         "other_game",
			userID:         "test_user_other_123",
			unionID:        "test_union_id_123",
			expectedResult: false,
			description:    "测试当game_id不是配置的新游戏ID时，应该跳过用户ID映射",
		},
		{
			name:           "空UnionID跳过映射测试",
			gameID:         config.GlobConfig.UserIDMapping.NewGameID, // 使用配置的新游戏ID
			userID:         "test_user_kof_123",
			unionID:        "",
			expectedResult: false,
			description:    "测试当UnionID为空时，应该跳过用户ID映射",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 注意：由于我们没有完整的数据库和服务层，这里主要测试映射逻辑
			// 在实际测试中，我们需要模拟service层的响应

			var result bool
			if tt.unionID == "" {
				// 对于空unionID的情况，使用包含unionID检查的函数
				result = messageLogic.shouldMapUserIDWithUnion(tt.gameID, tt.unionID)
			} else {
				// 对于其他情况，使用基本的检查函数
				result = messageLogic.shouldMapUserID(tt.gameID)
			}
			assert.Equal(t, tt.expectedResult, result, tt.description)
		})
	}
}

// 辅助函数：检查是否应该进行用户映射
func (m *MessageLogic) shouldMapUserID(gameID string) bool {
	return config.GlobConfig.UserIDMapping.Enabled &&
		config.GlobConfig.UserIDMapping.NewGameID == gameID
}

// shouldMapUserIDWithUnion 检查是否应该进行用户映射（包含unionID检查）
func (m *MessageLogic) shouldMapUserIDWithUnion(gameID string, unionID string) bool {
	if unionID == "" {
		return false
	}
	return m.shouldMapUserID(gameID)
}

// TestSubMessageNotify_UserIDMapping_Disabled 测试映射功能禁用的情况
func TestSubMessageNotify_UserIDMapping_Disabled(t *testing.T) {
	// 设置映射功能禁用
	config.GlobConfig.UserIDMapping = config.UserIDMappingConf{
		Enabled:   false,
		NewGameID: "test_game_123",
		OldGameID: "mapped_game_456",
	}

	messageLogic := &MessageLogic{}

	// 即使gameID匹配，但功能禁用时也不应该映射
	result := messageLogic.shouldMapUserID("test_game_123")
	assert.False(t, result, "功能禁用时，不应该进行映射")
}

// TestMessageLogic_UserIDMappingIntegration 集成测试用户ID映射功能
func TestMessageLogic_UserIDMappingIntegration(t *testing.T) {
	// 跳过集成测试（需要真实数据库连接）
	t.Skip("跳过集成测试，需要真实数据库环境")
}

// TestUserService_GetMappedUserID 测试用户映射服务
func TestUserService_GetMappedUserID(t *testing.T) {
	// 跳过用户映射服务测试（需要真实配置）
	t.Skip("跳过用户映射服务测试，需要完整配置环境")
}
