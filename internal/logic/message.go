package logic

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/task"
	"github.com/hibiken/asynq"
	"gorm.io/gorm"
)

var (
	_messageOnce  sync.Once
	_messageLogic *MessageLogic
)

type MessageLogic struct {
	userService          *service.UserService
	minigameService      *service.MinigameService
	messageService       *service.MessageService
	douyinService        *service.DouyinService
	wechatMessageService *service.WechatMessageService
	douyinMessageService *service.DouyinMessageService
	alipayService        *service.AlipayService
}

func SingletonMessageLogic() *MessageLogic {
	_messageOnce.Do(func() {
		_messageLogic = &MessageLogic{
			userService:          service.SingletonUserService(),
			minigameService:      service.SingletonMinigameService(),
			messageService:       service.SingletonMessageService(),
			douyinService:        service.SingletonDouyinService(),
			wechatMessageService: service.SingletonWechatMessageService(),
			douyinMessageService: service.SingletonDouyinMessageService(),
			alipayService:        service.SingletonAlipayService(),
		}
	})
	return _messageLogic
}

// SubMessageNotify 订阅消息
//func (l *MessageLogic) SubMessageNotify(ctx context.Context, req *bean.SubMessageNotifyReq) (*bean.SubMessageNotifyRes, error) {
//	//  删除取消消息任务
//	if req.PushType == constants.MsgSubscribeDelayedPushType && req.Delay == constants.MsgSubscribeCancelDelay {
//		msg, err := l.messageService.GetSubscribeMessageByTaskName(ctx, req.TaskName)
//		if err != nil {
//			return nil, err
//		}
//		err = sdasdasd.Cancel(ctx, msg.TaskID)
//		if err != nil {
//			return nil, err
//		}
//		err = l.messageService.UpdateMessageNotifyStatus(ctx,
//			msg.ID, constants.WechatSubscribeMsgCancelDelayStatus, "")
//		if err != nil {
//			return nil, err
//		}
//		return &bean.SubMessageNotifyRes{
//			SuccessCount: 1,
//		}, nil
//	}
//
//	// 注意: 批量用户必须是同一个小游戏下的用户id
//	conf, err := l.minigameService.GetMinigameConfig(ctx, req.GameID)
//	if err != nil {
//		return nil, err
//	}
//
//	var successCount int32
//	for _, userID := range req.UserIDs {
//		info, err := l.userService.GetMinigameModel(ctx, userID)
//		if err != nil {
//			return nil, err
//		}
//		id, err := l.messageService.SubMessageNotify(ctx, userID, req)
//		if err != nil {
//			return nil, err
//		}
//
//		taskReq := sdasdasd.SubscribeMessageTaskReq{
//			ID:          id,
//			AccessToken: conf.AccessToken,
//			OpenID:      info.OpenID,
//			TemplateID:  req.TemplateID,
//			Page:        req.Page,
//			Data:        req.Data,
//		}
//		taskReqByte, err := json.Marshal(taskReq)
//		if err != nil {
//			return nil, err
//		}
//		// 判断PushType
//		var taskID string
//		if req.PushType == constants.MsgSubscribePushType {
//			taskID, err = sdasdasd.Submit(asynq.NewTask(sdasdasd.TypeSubscribeMessage, taskReqByte))
//			if err != nil {
//				logger.Logger.Errorf("SubMessageNotify sdasdasd.Submit err: %s", err.Error())
//				return nil, err
//			}
//		} else if req.PushType == constants.MsgSubscribeDelayedPushType {
//			taskID, err = sdasdasd.SubmitByDelay(asynq.NewTask(sdasdasd.TypeSubscribeMessage, taskReqByte),
//				time.Duration(req.Delay)*time.Second)
//			if err != nil {
//				logger.Logger.Errorf("SubMessageNotify sdasdasd.SubmitByDelay err: %s", err.Error())
//				return nil, err
//			}
//		}
//		// 更新taskID
//		err = l.messageService.UpdateMessageNotifyTaskID(ctx, id, taskID)
//		if err != nil {
//			return nil, err
//		}
//		successCount++
//	}
//	return &bean.SubMessageNotifyRes{
//		SuccessCount: successCount,
//	}, nil
//}

// SubMessageNotify 微信订阅消息
func (l *MessageLogic) SubMessageNotify(ctx context.Context, req *bean.SubMessageNotifyReq) (*bean.SubMessageNotifyRes, error) {
	logger.Logger.InfofCtx(ctx, "SubMessageNotify req: %+v", req)
	// Handle message cancellation
	if req.PushType == constants.MsgSubscribeDelayedPushType && req.Delay == constants.MsgSubscribeCancelDelay {
		return l.cancelSubscribeMessage(ctx, req)
	}

	return l.handleBatchUsers(ctx, req)
}

// DouyinSubMessageNotify 抖音订阅消息
func (l *MessageLogic) DouyinSubMessageNotify(ctx context.Context, req *bean.DouyinSubMessageNotifyReq) (*bean.DouyinSubMessageNotifyRes, error) {
	conf, err := l.douyinService.GetDouyinConf(ctx, req.GameID)
	if err != nil {
		return nil, err
	}
	// 根据userID 获取openID
	if len(req.UserIDs) == 0 {
		return nil, fmt.Errorf("DouyinSubMessageNotify userIDs is empty, gameID: %s", req.GameID)
	}
	// 保存到sub message
	if err := l.messageService.BatchSubMessageNotify(ctx, req.UserIDs,
		&bean.SubMessageNotifyReq{
			GameID:       req.GameID,
			PlatformType: req.PlatformType,
			TaskName:     req.TaskName,
			TemplateID:   req.TemplateID,
			Data:         req.Data,
			Page:         req.Page,
			PushType:     constants.MsgSubscribePushType,
		},
	); err != nil {
		return nil, err
	}
	openIDMap, err := l.userService.GetOpenIDMapByUserIDs(ctx, req.UserIDs)
	if err != nil {
		return nil, err
	}

	var (
		successCount   int32
		successUserIDs []string
		failUserIDs    []string
	)
	for openID := range openIDMap {
		_, err := l.douyinMessageService.FetchSubscribeMessage(ctx, &bean.DouyinSubMessageReq{
			AccessToken: conf.AccessToken,
			AppID:       conf.AppID,
			OpenID:      openID,
			TplID:       req.TemplateID,
			Data:        req.Data,
			Page:        req.Page,
		})
		if err != nil {
			failUserIDs = append(failUserIDs, openIDMap[openID])
			logger.Logger.WarnfCtx(ctx, "FetchSubscribeMessage err: %s, gameID: %s, userID: %s", err.Error(), req.GameID, openIDMap[openID])
			continue
		}
		successUserIDs = append(successUserIDs, openIDMap[openID])
		successCount++
	}
	if len(successUserIDs) > 0 {
		if err := l.messageService.UpdatesSubMessageNotifyStatus(ctx, successUserIDs, constants.WechatSubscribeMsgSuccessStatus); err != nil {
			return nil, err
		}
	}
	if len(failUserIDs) > 0 {
		if err := l.messageService.UpdatesSubMessageNotifyStatus(ctx, failUserIDs, constants.WechatSubscribeMsgFailStatus); err != nil {
			return nil, err
		}
	}
	return &bean.DouyinSubMessageNotifyRes{SuccessCount: successCount}, nil
}

// AlipaySubMessageNotify 支付宝订阅消息（支持延迟发送）
func (l *MessageLogic) AlipaySubMessageNotify(ctx context.Context, req *bean.AlipaySubMessageNotifyReq) (*bean.AlipaySubMessageNotifyRes, error) {
	logger.Logger.InfofCtx(ctx, "[AlipaySubMessageNotify] 请求参数: %+v", req)

	// Handle message cancellation - 处理取消延迟消息
	if req.PushType == constants.MsgSubscribeDelayedPushType && req.Delay == constants.MsgSubscribeCancelDelay {
		return l.cancelAlipaySubscribeMessage(ctx, req)
	}

	if len(req.UserIDs) == 0 {
		return nil, fmt.Errorf("AlipaySubMessageNotify userIDs is empty, gameID: %s", req.GameID)
	}

	logger.Logger.InfofCtx(ctx, "[AlipaySubMessageNotify] 开始处理支付宝消息订阅, gameID: %s, userCount: %d", req.GameID, len(req.UserIDs))

	// 0. 检查任务名称是否已存在，提前返回业务错误
	isExist, err := l.messageService.IsTaskNameExist(ctx, req.GameID, req.TaskName)
	if err != nil {
		return nil, err
	}
	if isExist {
		logger.Logger.DebugfCtx(ctx, "[AlipaySubMessageNotify] 任务名称已存在, gameID: %s, taskName: %s", req.GameID, req.TaskName)
		return nil, constants.ErrTaskNameIsExist
	}

	// 1. 批量创建订阅消息记录
	if err := l.messageService.BatchSubMessageNotifyForAlipay(ctx, req.UserIDs, req); err != nil {
		return nil, err
	}

	// 2. 查询支付宝用户映射
	u := store.QueryDB().AUserAlipay
	users, err := u.WithContext(ctx).Where(u.UserID.In(req.UserIDs...), u.IsDeleted.Zero()).Find()
	if err != nil {
		return nil, err
	}

	var successCount int32

	// 3. 为每个用户创建异步任务
	for _, user := range users {
		// 获取消息记录ID
		msgID, err := l.messageService.GetMessageIDByUserIDAndTaskName(ctx, user.UserID, req.TaskName)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "[AlipaySubMessageNotify] 获取消息记录ID失败: userID=%s, taskName=%s, err=%v", user.UserID, req.TaskName, err)
			continue
		}

		// 创建异步任务请求
		taskReq := bean.AlipaySubscribeMessageTaskReq{
			ID:         msgID,
			GameID:     req.GameID,
			OpenID:     user.OpenID,
			TemplateID: req.TemplateID,
			Page:       req.Page,
			Data:       req.Data,
		}

		// 提交异步任务（支持延迟推送）
		taskReqByte, err := json.Marshal(taskReq)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "[AlipaySubMessageNotify] 序列化任务数据失败: userID=%s, err=%v", user.UserID, err)
			continue
		}

		var taskID string
		switch req.PushType {
		case constants.MsgSubscribePushType:
			// 立即推送
			taskID, err = task.Submit(asynq.NewTask(task.TypeAlipaySubscribeMessage, taskReqByte))
		case constants.MsgSubscribeDelayedPushType:
			// 延迟推送
			taskID, err = task.SubmitByDelay(asynq.NewTask(task.TypeAlipaySubscribeMessage, taskReqByte), time.Duration(req.Delay)*time.Second)
		default:
			logger.Logger.WarnfCtx(ctx, "[AlipaySubMessageNotify] 不支持的推送类型: userID=%s, pushType=%s", user.UserID, req.PushType)
			continue
		}

		if err != nil {
			logger.Logger.WarnfCtx(ctx, "[AlipaySubMessageNotify] 提交异步任务失败: userID=%s, pushType=%s, err=%v", user.UserID, req.PushType, err)
			continue
		}

		// 更新任务ID
		if err := l.messageService.UpdateMessageNotifyTaskID(ctx, msgID, taskID); err != nil {
			logger.Logger.WarnfCtx(ctx, "[AlipaySubMessageNotify] 更新任务ID失败: userID=%s, taskID=%s, err=%v", user.UserID, taskID, err)
			continue
		}

		successCount++
		logger.Logger.InfofCtx(ctx, "[AlipaySubMessageNotify] 成功创建异步任务: userID=%s, taskID=%s", user.UserID, taskID)
	}

	logger.Logger.InfofCtx(ctx, "[AlipaySubMessageNotify] 支付宝消息订阅处理完成, 成功创建任务数: %d", successCount)

	return &bean.AlipaySubMessageNotifyRes{SuccessCount: successCount}, nil
}

// cancelSubscribeMessage handles the specific logic of message cancellation.
func (l *MessageLogic) cancelSubscribeMessage(ctx context.Context, req *bean.SubMessageNotifyReq) (*bean.SubMessageNotifyRes, error) {
	// 参数基本验证
	if req.TaskName == "" {
		logger.Logger.WarnfCtx(ctx, "取消订阅消息失败，TaskName为空, GameID: %s", req.GameID)
		return nil, constants.ErrDelayParamAbnormal
	}

	msg, err := l.messageService.GetSubscribeMessageByTaskName(ctx, req.TaskName)
	if err != nil {
		// 针对 delay 参数异常的友好错误处理
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Logger.WarnfCtx(ctx, "取消订阅消息失败，任务名称不存在, GameID: %s, TaskName: %s", req.GameID, req.TaskName)
			return nil, constants.ErrDelayParamAbnormal
		}
		// 其他数据库错误直接返回
		return nil, err
	}

	if err := task.Cancel(ctx, msg.TaskID); err != nil {
		return nil, err
	}
	if err := l.messageService.UpdateMessageNotifyStatus(ctx, msg.ID, constants.WechatSubscribeMsgCancelDelayStatus, ""); err != nil {
		return nil, err
	}
	return &bean.SubMessageNotifyRes{SuccessCount: 1}, nil
}

// handleBatchUsers goes through the user IDs and manages the notification sdasdasd.
func (l *MessageLogic) handleBatchUsers(ctx context.Context, req *bean.SubMessageNotifyReq) (*bean.SubMessageNotifyRes, error) {
	conf, err := l.minigameService.GetMinigameConfig(ctx, req.GameID)
	if err != nil {
		return nil, err
	}

	var successCount int32
	for _, userID := range req.UserIDs {
		if err := l.processSingleUser(ctx, conf, userID, req); err != nil {
			return nil, err
		}
		successCount++
	}
	return &bean.SubMessageNotifyRes{SuccessCount: successCount}, nil
}

// processSingleUser handles the notification logic for a single user.
func (l *MessageLogic) processSingleUser(ctx context.Context, conf *model.AConfigMinigame, userID string, req *bean.SubMessageNotifyReq) error {
	// 用户ID映射功能 - 临时过渡逻辑
	// TODO: 此功能为临时过渡逻辑，后续需要完全移除
	mappedUserID := userID
	info, err := l.userService.GetMinigameModel(ctx, userID)
	if err != nil {
		return err
	}

	// 特别针对配置的新游戏ID时，从旧游戏映射用户ID
	if req.GameID == config.GlobConfig.UserIDMapping.NewGameID && info.UnionID != "" {
		if mappedID, mappingErr := l.userService.GetSubscribeMappedUserID(ctx, info.UnionID, req.GameID); mappingErr == nil && mappedID != "" {
			logger.Logger.InfofCtx(ctx, "[消息订阅] 用户ID映射成功, 原UserID: %s, 映射UserID: %s, UnionID: %s, GameID: %s",
				userID, mappedID, info.UnionID, req.GameID)

			// 使用映射后的UserID
			mappedUserID = mappedID

			// 重新获取映射后用户的信息
			if mappedInfo, mappedErr := l.userService.GetMinigameModel(ctx, mappedUserID); mappedErr == nil {
				info = mappedInfo
			} else {
				logger.Logger.WarnfCtx(ctx, "[消息订阅] 获取映射用户信息失败, 映射UserID: %s, 错误: %v", mappedUserID, mappedErr)
			}
		} else {
			logger.Logger.InfofCtx(ctx, "[消息订阅] 用户ID映射未找到对应用户, 原UserID: %s, UnionID: %s, GameID: %s",
				userID, info.UnionID, req.GameID)
		}
	}

	isExist, err := l.messageService.IsTaskNameExist(ctx, req.GameID, req.TaskName)
	if err != nil {
		return err
	}
	if isExist {
		return constants.ErrTaskNameIsExist
	}

	// 使用映射后的用户ID进行后续处理
	id, err := l.messageService.SubMessageNotify(ctx, userID, req) // TODO 使用旧userID 完成存储
	if err != nil {
		return err
	}

	taskReq := bean.SubscribeMessageTaskReq{
		ID:          id,
		AccessToken: conf.AccessToken,
		OpenID:      info.OpenID, // TODO 新用户ID
		TemplateID:  req.TemplateID,
		Page:        req.Page,
		Data:        req.Data,

		// 设置回退信息 - 临时过渡逻辑
		OriginalUserID: userID,                                    // 原始用户ID
		OriginalGameID: config.GlobConfig.UserIDMapping.OldGameID, // 原始游戏ID (hlxq)
		IsMapped:       mappedUserID != userID,                    // 是否进行了映射
	}
	if err := l.submitTask(ctx, req, taskReq); err != nil {
		return err
	}

	return nil
}

// submitTask submits the notification sdasdasd based on the PushType.
func (l *MessageLogic) submitTask(ctx context.Context, req *bean.SubMessageNotifyReq, taskReq bean.SubscribeMessageTaskReq) error {
	taskReqByte, err := json.Marshal(taskReq)
	if err != nil {
		return err
	}
	var taskID string
	switch req.PushType {
	case constants.MsgSubscribePushType:
		taskID, err = task.Submit(asynq.NewTask(task.TypeSubscribeMessage, taskReqByte))
	case constants.MsgSubscribeDelayedPushType:
		taskID, err = task.SubmitByDelay(asynq.NewTask(task.TypeSubscribeMessage, taskReqByte), time.Duration(req.Delay)*time.Second)
	}
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "Task submission error: %s", err.Error())
		return err
	}
	return l.messageService.UpdateMessageNotifyTaskID(ctx, taskReq.ID, taskID)
}

// cancelAlipaySubscribeMessage 取消支付宝延迟订阅消息
func (l *MessageLogic) cancelAlipaySubscribeMessage(ctx context.Context, req *bean.AlipaySubMessageNotifyReq) (*bean.AlipaySubMessageNotifyRes, error) {
	if req.TaskName == "" {
		logger.Logger.WarnfCtx(ctx, "取消支付宝订阅消息失败，TaskName为空, GameID: %s", req.GameID)
		return nil, constants.ErrDelayParamAbnormal
	}

	msg, err := l.messageService.GetSubscribeMessageByTaskName(ctx, req.TaskName)
	if err != nil {
		// 针对 delay 参数异常的友好错误处理
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Logger.WarnfCtx(ctx, "取消支付宝订阅消息失败，任务名称不存在, GameID: %s, TaskName: %s", req.GameID, req.TaskName)
			return nil, constants.ErrDelayParamAbnormal
		}
		// 其他数据库错误直接返回
		return nil, err
	}

	if err := task.Cancel(ctx, msg.TaskID); err != nil {
		return nil, err
	}
	if err := l.messageService.UpdateMessageNotifyStatus(ctx, msg.ID, constants.WechatSubscribeMsgCancelDelayStatus, ""); err != nil {
		return nil, err
	}
	return &bean.AlipaySubMessageNotifyRes{SuccessCount: 1}, nil
}
