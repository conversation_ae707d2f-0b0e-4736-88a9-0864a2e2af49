package logic

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"unicode/utf8"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/cron"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/internal/store/model"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
)

// 轻量封装：从cron预计算缓存中获取词与签名
type precomputedSet struct {
	words     []string
	signature string
}

func cronPrecomputedOriginal(gameID string) (precomputedSet, bool) {
	wordsV, ok1 := cronPrecomputedLoadWords(cronPrecomputedOriginalWords, gameID)
	sigV, ok2 := cronPrecomputedLoadSig(cronPrecomputedOriginalSig, gameID)
	if !ok1 || !ok2 {
		return precomputedSet{}, false
	}
	return precomputedSet{words: wordsV, signature: sigV}, true
}

func cronPrecomputedLower(gameID string) (precomputedSet, bool) {
	wordsV, ok1 := cronPrecomputedLoadWords(cronPrecomputedLowerWords, gameID)
	sigV, ok2 := cronPrecomputedLoadSig(cronPrecomputedLowerSig, gameID)
	if !ok1 || !ok2 {
		return precomputedSet{}, false
	}
	return precomputedSet{words: wordsV, signature: sigV}, true
}

// 通过函数变量避免循环依赖（由cron包提供具体实现）
var (
	cronPrecomputedOriginalWords = cron.GetPrecomputedOriginalWords
	cronPrecomputedOriginalSig   = cron.GetPrecomputedOriginalSig
	cronPrecomputedLowerWords    = cron.GetPrecomputedLowerWords
	cronPrecomputedLowerSig      = cron.GetPrecomputedLowerSig
)

func cronPrecomputedLoadWords(fn func(string) ([]string, bool), gameID string) ([]string, bool) {
	if fn == nil {
		return nil, false
	}
	return fn(gameID)
}

func cronPrecomputedLoadSig(fn func(string) (string, bool), gameID string) (string, bool) {
	if fn == nil {
		return "", false
	}
	return fn(gameID)
}

var (
	_sensitiveOnce  sync.Once
	_sensitiveLogic *SensitiveLogic
)

type SensitiveLogic struct {
	sensitiveService    *service.SensitiveService
	minigameService     *service.MinigameService
	userService         *service.UserService
	trieService         *service.TrieService
	douyinService       *service.DouyinService
	tencentCloudService *service.TencentCloudService
	alipayService       *service.AlipayService
}

func SingletonSensitiveLogic() *SensitiveLogic {
	_sensitiveOnce.Do(func() {
		_sensitiveLogic = &SensitiveLogic{
			sensitiveService:    service.SingletonSensitiveService(),
			minigameService:     service.SingletonMinigameService(),
			userService:         service.SingletonUserService(),
			trieService:         service.SingletonTrieService(),
			douyinService:       service.SingletonDouyinService(),
			tencentCloudService: service.SingletonTencentCloudService(),
			alipayService:       service.SingletonAlipayService(),
		}
	})
	return _sensitiveLogic
}

// VerifySensitiveMessage 敏感词检测
func (l *SensitiveLogic) VerifySensitiveMessage(ctx context.Context, req *bean.VerifySensitiveMessageReq) (*bean.VerifySensitiveMessageRes, error) {
	logger.Logger.Infof("SensitiveLogic VerifySensitiveMessage req: %+v", req)
	if len(req.Msg) > constants.SensitiveWordMaxLength {
		return nil, fmt.Errorf("SensitiveService VerifySensitiveMessage msg length > 2500")
	}

	resp := &bean.VerifySensitiveMessageRes{}
	var err error
	switch req.PlatformType {
	case constants.PlatformTypeMinigame:
		resp, err = l.wechatSensitiveMessage(ctx, req.GameID, req.UserID, req.Msg, req.PlatformType)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "SensitiveLogic wechatSensitiveMessage error, Triggered Tencent Cloud: %v", err)
			// 触发腾讯云cloud文本检查
			tencentCloudResp, err := l.tencentCloudService.FatchTencentCloudCheckText(ctx, req.Msg, req.PlatformType)
			if err != nil {
				logger.Logger.ErrorfCtx(ctx, "SensitiveLogic wechat FatchTencentCloudCheckText error: %v", err)
				return nil, err
			}
			resp = tencentCloudResp
		}
	case constants.PlatformTypeDouyin:
		resp, err = l.douyinSensitiveMessage(ctx, req.GameID, req.Msg)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "SensitiveLogic douyinSensitiveMessage error, Triggered Tencent Cloud: %v", err)
			// 触发腾讯云cloud文本检查
			tencentCloudResp, err := l.tencentCloudService.FatchTencentCloudCheckText(ctx, req.Msg, req.PlatformType)
			if err != nil {
				logger.Logger.ErrorfCtx(ctx, "SensitiveLogic douyin FatchTencentCloudCheckText error: %v", err)
				return nil, err
			}
			resp = tencentCloudResp
		}
	case constants.PlatformTypeAlipayMinigame:
		resp, err = l.alipaySensitiveMessage(ctx, req.GameID, req.UserID, req.Msg)
		if err != nil {
			logger.Logger.WarnfCtx(ctx, "SensitiveLogic alipaySensitiveMessage error, Triggered Tencent Cloud: %v", err)
			// 触发腾讯云cloud文本检查
			tencentCloudResp, err := l.tencentCloudService.FatchTencentCloudCheckText(ctx, req.Msg, req.PlatformType)
			if err != nil {
				logger.Logger.ErrorfCtx(ctx, "SensitiveLogic alipay FatchTencentCloudCheckText error: %v", err)
				return nil, err
			}
			resp = tencentCloudResp
		}
	}

	wordMap := cron.GetSensitiveWordLevelByGameID(req.GameID)
	ignoreCase := cron.GetSensitiveWordConfigByGameID(req.GameID)
	// 基于预计算缓存构建/复用Trie，避免每次请求排序/签名
	if len(wordMap) > 0 {
		if ignoreCase == 1 {
			if v, ok := cronPrecomputedLower(req.GameID); ok {
				l.trieService.EnsureGameTriePrecomputed(req.GameID, ignoreCase, v.words, v.signature)
			} else {
				// 预计算未命中，降级为基于 wordMap 的一次性构建
				logger.Logger.DebugfCtx(ctx, "SensitiveLogic precomputed lower missing, fallback EnsureGameTrie. gameID=%s", req.GameID)
				wordsForTrie := make([]string, 0, len(wordMap))
				for w := range wordMap {
					wordsForTrie = append(wordsForTrie, w)
				}
				l.trieService.EnsureGameTrie(req.GameID, ignoreCase, wordsForTrie)
			}
		} else {
			if v, ok := cronPrecomputedOriginal(req.GameID); ok {
				l.trieService.EnsureGameTriePrecomputed(req.GameID, ignoreCase, v.words, v.signature)
			} else {
				// 预计算未命中，降级为基于 wordMap 的一次性构建
				logger.Logger.DebugfCtx(ctx, "SensitiveLogic precomputed original missing, fallback EnsureGameTrie. gameID=%s", req.GameID)
				wordsForTrie := make([]string, 0, len(wordMap))
				for w := range wordMap {
					wordsForTrie = append(wordsForTrie, w)
				}
				l.trieService.EnsureGameTrie(req.GameID, ignoreCase, wordsForTrie)
			}
		}
	}

	// 利用反查询把词汇和等级找出（基于按游戏缓存的Trie）
	words := l.trieService.CheckByCaseForGame(req.Msg, req.GameID, ignoreCase)

	// A方案：请求侧构建大小写无关等级表，解决 ignoreCase==1 时等级获取不命中
	var levelLookup map[string]int32
	if ignoreCase == 1 {
		levelLookup = make(map[string]int32, len(wordMap))
		for k, v := range wordMap {
			lk := strings.ToLower(k)
			if v > levelLookup[lk] {
				levelLookup[lk] = v
			}
		}
	} else {
		levelLookup = wordMap
	}

	var detail []*bean.SensitiveMessagePlatformDetail
	for _, word := range words {
		var lvl int32
		if ignoreCase == 1 {
			lvl = levelLookup[strings.ToLower(word)]
		} else {
			lvl = levelLookup[word]
		}
		if lvl == 0 {
			continue
		}
		detail = append(detail, &bean.SensitiveMessagePlatformDetail{
			Keyword: word,
			Level:   lvl,
		})
	}
	resp.PlatformDetail = detail

	if resp.ReplacedContent != "" {
		resp.ReplacedContent = l.trieService.CheckAndReplaceByCaseForGame(resp.ReplacedContent, req.GameID, ignoreCase)
		return resp, nil
	}
	resp.ReplacedContent = l.trieService.CheckAndReplaceByCaseForGame(req.Msg, req.GameID, ignoreCase)
	return resp, nil
}

// MapUserIDForKofGame 为kof游戏映射用户ID（临时过渡逻辑）
// TODO: 此功能为临时过渡逻辑，后续需要完全移除
func (l *SensitiveLogic) MapUserIDForKofGame(ctx context.Context, userID, gameID, logPrefix string) (mappedUserID string, mappedUser *model.AUserMinigame, err error) {
	mappedUserID = userID
	user, err := l.userService.GetMinigameModel(ctx, userID)
	if err != nil {
		return "", nil, err
	}

	// 配置驱动：仅当开启映射开关且当前游戏ID等于新游戏ID且存在UnionID时执行映射逻辑
	mapConf := config.GlobConfig.UserIDMapping
	if mapConf.Enabled && gameID == mapConf.NewGameID && user.UnionID != "" {
		// 检查用户在 hlxq 和 kof 游戏中的账户数量，判断是否为新用户（批量一次查询代替两次调用）
		userIDsMap, batchErr := l.userService.GetUserIDsByUnionIDSafe(ctx, user.UnionID, []string{config.GlobConfig.UserIDMapping.OldGameID, config.GlobConfig.UserIDMapping.NewGameID})
		hlxqUserID := userIDsMap[config.GlobConfig.UserIDMapping.OldGameID]
		kofUserID := userIDsMap[config.GlobConfig.UserIDMapping.NewGameID]
		// 为保持原日志语义，构造与之前相似的错误变量（批量安全查询失败时忽略）
		var hlxqErr error
		var kofErr error
		_ = batchErr // 批量安全查询失败等价于两个均空，不影响主流程

		// 统计找到的有效账户数量
		accountCount := 0
		if hlxqErr == nil && hlxqUserID != "" {
			accountCount++
		}
		if kofErr == nil && kofUserID != "" {
			accountCount++
		}

		logger.Logger.InfofCtx(ctx, "[%s] 用户账户检查, UnionID: %s, hlxq用户: %s, kof用户: %s, 账户数量: %d",
			logPrefix, user.UnionID, hlxqUserID, kofUserID, accountCount)

		// 如果只有一个账户，说明是新用户，无需映射
		if accountCount <= 1 {
			logger.Logger.InfofCtx(ctx, "[%s] 检测到新用户，无需进行用户ID映射, 原UserID: %s, UnionID: %s, GameID: %s",
				logPrefix, userID, user.UnionID, gameID)
		} else {
			// 有多个账户，说明是老用户，需要进行映射
			// 使用 kof 游戏中的用户ID作为映射结果
			if kofUserID != "" {
				logger.Logger.InfofCtx(ctx, "[%s] 用户ID映射成功, 原UserID: %s, 映射UserID: %s, UnionID: %s, GameID: %s",
					logPrefix, userID, kofUserID, user.UnionID, gameID)

				// 使用映射后的UserID
				mappedUserID = kofUserID

				// 重新获取映射后用户的信息
				if mappedInfo, mappedErr := l.userService.GetMinigameModel(ctx, mappedUserID); mappedErr == nil {
					user = mappedInfo
				} else {
					logger.Logger.WarnfCtx(ctx, "[%s] 获取映射用户信息失败, 映射UserID: %s, 错误: %v", logPrefix, mappedUserID, mappedErr)
				}
			} else {
				logger.Logger.InfofCtx(ctx, "[%s] 用户ID映射未找到对应用户, 原UserID: %s, UnionID: %s, GameID: %s",
					logPrefix, userID, user.UnionID, gameID)
			}
		}
	}

	return mappedUserID, user, nil
}

func (l *SensitiveLogic) wechatSensitiveMessage(ctx context.Context, gameID, userID, msg, platformType string) (*bean.VerifySensitiveMessageRes, error) {
	resp := &bean.VerifySensitiveMessageRes{}
	conf, err := l.minigameService.GetMinigameConfig(ctx, gameID)
	if err != nil {
		return nil, err
	}

	// 用户ID映射功能 - 临时过渡逻辑
	// TODO: 此功能为临时过渡逻辑，后续需要完全移除
	mappedUserID, user, err := l.MapUserIDForKofGame(ctx, userID, gameID, "敏感词检查")
	if err != nil {
		return nil, err
	}

	// 使用映射后的用户信息进行敏感词检查
	logger.Logger.InfofCtx(ctx, "[敏感词检查] 使用UserID进行敏感词检查: %s, OpenID: %s", mappedUserID, user.OpenID)
	result, err := l.sensitiveService.VerifySensitiveMessage(ctx, user.OpenID, conf.AccessToken, msg)
	if err != nil {
		return nil, err
	}
	resp.TraceID = result.TraceID
	resp.ErrCode = result.ErrCode
	resp.ErrMsg = result.ErrMsg
	resp.Source = constants.PlatformTypeMinigame
	resp.Result = result.Result
	resp.Detail = result.Detail
	resp.ReplacedContent = result.Result.ReplacedContent
	return resp, nil
}

func (l *SensitiveLogic) douyinSensitiveMessage(ctx context.Context, gameID, msg string) (*bean.VerifySensitiveMessageRes, error) {
	resp := &bean.VerifySensitiveMessageRes{}
	conf, err := l.douyinService.GetDouyinConf(ctx, gameID)
	if err != nil {
		return nil, err
	}
	result, err := l.sensitiveService.VerifyDouyinSensitiveMessage(ctx, conf.AccessToken, msg)
	if err != nil {
		return nil, err
	}
	if len(result.Data) == 0 {
		return nil, fmt.Errorf("VerifyDouyinSensitiveMessage data len is zero, gameID: %s", gameID)
	}
	r := result.Data[0]
	resp.TraceID = result.LogID
	resp.ErrCode = r.Code
	resp.ErrMsg = r.Msg
	resp.Source = constants.PlatformTypeDouyin
	resp.Detail = r.Predicts
	resp.Result = l.handleDouyinSensitiveResult(ctx, msg, r.Predicts)
	if resp.Result.ReplacedContent != "" {
		resp.ReplacedContent = resp.Result.ReplacedContent
	}
	return resp, nil
}

func (l *SensitiveLogic) handleDouyinSensitiveResult(ctx context.Context, msg string, predicts []bean.DouyinSecurityPredict) bean.WechatSecurityCheckResult {
	// 遍历predicts，找到第一个hit为true的，然后返回
	// 检测结果-置信度-概率，值为 0 或者 1，当值为 1 时表示检测的文本包含违法违规内容
	for _, predict := range predicts {
		if predict.Prob == constants.SensitiveProbRisk {
			return bean.WechatSecurityCheckResult{
				Suggest:         constants.SensitiveSuggestRisk,
				Label:           constants.SensitiveLabelRisk,
				ReplacedContent: l.replaceSensitiveWord(msg),
			}
		}
	}
	return bean.WechatSecurityCheckResult{
		Suggest:         constants.SensitiveSuggestPass,
		Label:           constants.SensitiveLabelPass,
		ReplacedContent: msg,
	}
}

func (l *SensitiveLogic) replaceSensitiveWord(msg string) string {
	// 如果发现prob为1，则将msg中的蚊子全部替换为*
	charCount := utf8.RuneCountInString(msg)
	return strings.Repeat("*", charCount)
}

// alipaySensitiveMessage 支付宝敏感词检测
func (l *SensitiveLogic) alipaySensitiveMessage(ctx context.Context, gameID, userID, msg string) (*bean.VerifySensitiveMessageRes, error) {
	resp := &bean.VerifySensitiveMessageRes{}

	// 通过 user_id 获取 open_id
	openID, err := l.alipayService.GetOpenIDByUserID(ctx, userID)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "SensitiveLogic alipaySensitiveMessage failed to get open_id, userID: %s, error: %v", userID, err)
		return nil, err
	}
	if openID == "" {
		logger.Logger.WarnfCtx(ctx, "SensitiveLogic alipaySensitiveMessage open_id not found, userID: %s", userID)
		// 未找到open_id默认返回不通过敏感词检查
		resp.ErrCode = 1
		resp.ErrMsg = "用户身份验证失败"
		resp.Source = constants.PlatformTypeAlipayMinigame
		return resp, nil
	}

	// 使用 open_id 进行内容检测
	result, err := l.alipayService.VerifyAlipayContentWithOpenID(ctx, gameID, openID, msg)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "SensitiveLogic alipaySensitiveMessage failed, gameID: %s, openID: %s, error: %v", gameID, openID, err)
		return nil, err
	}
	resp.Source = constants.PlatformTypeAlipayMinigame

	// 类似抖音的错误处理方式：使用实际的结果状态
	resp.ErrCode = 0
	resp.ErrMsg = "ok"
	// 支付宝API调用成功，设置默认的成功状态

	// 类似抖音的处理方式：直接使用原始结果
	if result != nil {
		resp.Detail = result.DetectCheckLabels

		// 类似抖音的简单处理方式
		resp.Result = l.handleAlipaySensitiveResult(ctx, msg, result)

		// 设置TraceID，类似抖音使用LogID的方式
		if result.RequestID != "" {
			resp.TraceID = result.RequestID
		}
	}

	// 保持和抖音统一的返回逻辑
	if resp.Result.ReplacedContent != "" {
		resp.ReplacedContent = resp.Result.ReplacedContent
	}
	return resp, nil
}

// handleAlipaySensitiveResult 处理支付宝敏感词检测结果，类似抖音的处理方式
func (l *SensitiveLogic) handleAlipaySensitiveResult(_ context.Context, msg string, alipayResult *bean.AlipayContentCheckResult) bean.WechatSecurityCheckResult {
	// 类似抖音的处理方式：基于suggestion字段判断风险
	// 当suggestion为"block"或"review"时，表示检测到风险内容
	if alipayResult.Suggestion == "block" || alipayResult.Suggestion == "review" {
		return bean.WechatSecurityCheckResult{
			Suggest:         constants.SensitiveSuggestRisk,
			Label:           constants.SensitiveLabelRisk,
			ReplacedContent: l.replaceSensitiveWord(msg),
		}
	}

	// 其他情况（如"pass"）表示通过
	return bean.WechatSecurityCheckResult{
		Suggest:         constants.SensitiveSuggestPass,
		Label:           constants.SensitiveLabelPass,
		ReplacedContent: msg,
	}
}
