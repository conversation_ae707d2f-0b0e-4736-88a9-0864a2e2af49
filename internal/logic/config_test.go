package logic

import (
	"testing"
)

func TestExactPlatformMatch_AlipaySingle(t *testing.T) {
	l := &ConfigLogic{}
	if !l.exactPlatformMatch("alipay_minigame", "alipay_minigame") {
		t.Fatalf("expected true for exact match alipay_minigame")
	}
}

func TestExactPlatformMatch_AlipayMultiple(t *testing.T) {
	l := &ConfigLogic{}
	platforms := "minigame,douyin_minigame, alipay_minigame"
	if !l.exactPlatformMatch(platforms, "alipay_minigame") {
		t.Fatalf("expected true when alipay_minigame is in multi-platform list")
	}
}

func TestExactPlatformMatch_NoPartial(t *testing.T) {
	l := &ConfigLogic{}
	if l.exactPlatformMatch("alipay", "alipay_minigame") {
		t.Fatalf("expected false for partial match (alipay vs alipay_minigame)")
	}
}
