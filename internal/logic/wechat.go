package logic

import (
	"context"
	"encoding/json"
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
)

var (
	_wechatOnce  sync.Once
	_wechatLogic *WechatLogic
)

type WechatLogic struct {
	secretService   *service.SecretService
	minigameService *service.MinigameService
	userService     *service.UserService
}

func SingletonWechatLogic() *WechatLogic {
	_wechatOnce.Do(func() {
		_wechatLogic = &WechatLogic{
			secretService:   service.SingletonSecretService(),
			minigameService: service.SingletonMinigameService(),
			userService:     service.SingletonUserService(),
		}
	})
	return _wechatLogic
}

// DecryptData 微信数据解密
func (l *WechatLogic) DecryptData(ctx context.Context, req *bean.WechatDecryptReq) (*bean.WechatDecryptResp, error) {
	logger.Logger.InfofCtx(ctx, "[WechatLogic.DecryptData] 开始处理微信解密请求, game_id: %s, user_id: %s", req.GameID, req.UserID)

	// 根据用户ID获取小程序用户信息
	userMinigame, err := l.userService.GetMinigameModel(ctx, req.UserID)
	if err != nil {
		logger.Logger.InfofCtx(ctx, "[WechatLogic.DecryptData] 获取用户小程序信息失败, game_id: %s, user_id: %s, err: %v", req.GameID, req.UserID, err)
		return nil, err
	}
	if userMinigame == nil {
		logger.Logger.InfofCtx(ctx, "[WechatLogic.DecryptData] 用户小程序信息不存在, game_id: %s, user_id: %s", req.GameID, req.UserID)
		return nil, constants.ErrUserNotExists
	}

	// 这里需要根据实际业务逻辑来校验signature
	// 目前直接进行解密，后续可以根据需要添加signature校验逻辑
	logger.Logger.InfofCtx(ctx, "[WechatLogic.DecryptData] 开始解密数据, game_id: %s, user_id: %s", req.GameID, req.UserID)

	// 使用用户的session key进行解密
	decryptedStr, err := l.secretService.WechatDataDecryptStr(userMinigame.SessionKey, req.EncryptedData, req.IV)
	if err != nil {
		logger.Logger.InfofCtx(ctx, "[WechatLogic.DecryptData] 数据解密失败, game_id: %s, user_id: %s, err: %v", req.GameID, req.UserID, err)
		return nil, err
	}

	logger.Logger.InfofCtx(ctx, "[WechatLogic.DecryptData] 数据解密成功, game_id: %s, user_id: %s", req.GameID, req.UserID)

	// 尝试将解密后的字符串解析为JSON
	var decryptedData interface{}
	if err := json.Unmarshal([]byte(decryptedStr), &decryptedData); err != nil {
		// 如果不是JSON格式，直接返回字符串
		logger.Logger.InfofCtx(ctx, "[WechatLogic.DecryptData] 解密数据非JSON格式，返回原始字符串, game_id: %s, user_id: %s", req.GameID, req.UserID)
		decryptedData = decryptedStr
	}

	resp := &bean.WechatDecryptResp{
		Data: decryptedData,
	}

	logger.Logger.InfofCtx(ctx, "[WechatLogic.DecryptData] 微信解密处理完成, game_id: %s, user_id: %s", req.GameID, req.UserID)
	return resp, nil
}
