package logic

import (
	"testing"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"github.com/stretchr/testify/assert"
)

// TestVerifySensitiveMessage_UserIDMapping 测试敏感词检查中的用户ID映射功能
func TestVerifySensitiveMessage_UserIDMapping(t *testing.T) {
	// 简单初始化，设置一个测试用的映射配置
	config.GlobConfig.UserIDMapping = config.UserIDMappingConf{
		Enabled:   true,
		NewGameID: "test_game_123",
		OldGameID: "mapped_game_456",
	}

	sensitiveLogic := &SensitiveLogic{}

	tests := []struct {
		name           string
		gameID         string
		userID         string
		platformType   string
		msg            string
		unionID        string
		expectedResult bool
		description    string
	}{
		{
			name:           "微信小游戏敏感词检查映射测试",
			gameID:         config.GlobConfig.UserIDMapping.NewGameID, // 使用配置的新游戏ID
			userID:         "test_user_hlxq_123",
			platformType:   constants.PlatformTypeMinigame, // "minigame"
			msg:            "测试敏感词检查消息",
			unionID:        "test_union_id_123",
			expectedResult: true,
			description:    "测试当game_id为配置的新游戏ID且platform_type为minigame时，用户ID映射功能是否正常工作",
		},
		{
			name:           "抖音小游戏跳过映射测试",
			gameID:         config.GlobConfig.UserIDMapping.NewGameID,
			userID:         "test_user_douyin_123",
			platformType:   constants.PlatformTypeDouyin, // "douyin_minigame"
			msg:            "测试抖音敏感词检查消息",
			unionID:        "test_union_id_123",
			expectedResult: false,
			description:    "测试当platform_type为douyin_minigame时，应该跳过用户ID映射",
		},
		{
			name:           "非配置游戏ID跳过映射测试",
			gameID:         "other_game",
			userID:         "test_user_other_123",
			platformType:   constants.PlatformTypeMinigame,
			msg:            "测试其他游戏敏感词检查消息",
			unionID:        "test_union_id_123",
			expectedResult: false,
			description:    "测试当game_id不是配置的新游戏ID时，应该跳过用户ID映射",
		},
		{
			name:           "空UnionID跳过映射测试",
			gameID:         config.GlobConfig.UserIDMapping.NewGameID,
			userID:         "test_user_no_union_123",
			platformType:   constants.PlatformTypeMinigame,
			msg:            "测试空UnionID敏感词检查消息",
			unionID:        "",
			expectedResult: false,
			description:    "测试当UnionID为空时，应该跳过用户ID映射",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 注意：由于我们没有完整的数据库和服务层，这里主要测试映射逻辑
			// 在实际测试中，我们需要模拟service层的响应

			var result bool
			if tt.unionID == "" {
				// 对于空unionID的情况，使用包含unionID检查的函数
				result = sensitiveLogic.shouldMapUserIDWithUnion(tt.gameID, tt.platformType, tt.unionID)
			} else {
				// 对于其他情况，使用基本的检查函数
				result = sensitiveLogic.shouldMapUserID(tt.gameID, tt.platformType)
			}
			assert.Equal(t, tt.expectedResult, result, tt.description)
		})
	}
}

// 辅助函数：检查是否应该进行用户映射
func (s *SensitiveLogic) shouldMapUserID(gameID string, platformType string) bool {
	// 检查平台类型是否为微信小游戏
	if platformType != constants.PlatformTypeMinigame {
		return false
	}

	return config.GlobConfig.UserIDMapping.Enabled &&
		config.GlobConfig.UserIDMapping.NewGameID == gameID
}

// shouldMapUserIDWithUnion 检查是否应该进行用户映射（包含unionID检查）
func (s *SensitiveLogic) shouldMapUserIDWithUnion(gameID string, platformType string, unionID string) bool {
	if unionID == "" {
		return false
	}
	return s.shouldMapUserID(gameID, platformType)
}

// TestSensitiveLogic_UserIDMappingIntegration 集成测试敏感词检查中的用户ID映射功能
func TestSensitiveLogic_UserIDMappingIntegration(t *testing.T) {
	// 跳过集成测试（需要真实数据库连接）
	t.Skip("跳过集成测试，需要真实数据库环境")
}

// TestWechatSensitiveMessage_UserIDMapping 测试微信敏感词检查方法中的用户ID映射
func TestWechatSensitiveMessage_UserIDMapping(t *testing.T) {
	// 跳过单元测试（需要真实配置）
	t.Skip("跳过微信敏感词检查测试，需要完整配置环境")
}

// TestSensitiveLogic_PlatformTypeMapping 测试不同平台类型的映射行为
func TestSensitiveLogic_PlatformTypeMapping(t *testing.T) {
	// 跳过平台类型映射测试（需要完整环境）
	t.Skip("跳过平台类型映射测试，需要完整测试环境")
}
