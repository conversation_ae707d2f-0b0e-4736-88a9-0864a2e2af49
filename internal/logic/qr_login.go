package logic

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/constants"
	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/pkg/util"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
	"github.com/google/uuid"
	"github.com/skip2/go-qrcode"
)

type QRLoginLogic struct {
	qrLoginService *service.QRLoginService
	userService    *service.UserService
	douyinService  *service.DouyinService
}

var (
	_qrLoginLogic *QRLoginLogic
)

func SingletonQRLoginLogic() *QRLoginLogic {
	if _qrLoginLogic == nil {
		_qrLoginLogic = &QRLoginLogic{
			qrLoginService: service.SingletonQRLoginService(),
			userService:    service.SingletonUserService(),
			douyinService:  service.SingletonDouyinService(),
		}
	}
	return _qrLoginLogic
}

// GenerateUUIDQRCode 生成新的UUID二维码，并使之前的UUID失效
func (l *QRLoginLogic) GenerateUUIDQRCode(ctx context.Context, req *bean.GenerateQRLoginReq) (*bean.GenerateQRLoginResp, error) {
	// 获取旧的激活uuid并作废
	// 从JWT中，user_id已注入到Header，Bind会填充到 req.Header.UserID
	userID := req.UserID
	if userID == "" {
		return nil, constants.ErrAuthFailed
	}
	ctxName := "default"
	oldUUID, err := l.qrLoginService.GetActiveUUID(ctx, userID, ctxName)
	if err != nil {
		return nil, err
	}
	if oldUUID != "" {
		_ = l.qrLoginService.InvalidateUUID(ctx, oldUUID)
	}

	// 生成新UUID
	newUUID := uuid.NewString()

	// 记录为激活uuid并写入uuid->userID，TTL=5分钟
	if err := l.qrLoginService.SetActiveUUID(ctx, userID, ctxName, newUUID); err != nil {
		return nil, err
	}

	// 保存UUID到UserID的映射，用于隐藏用户ID，TTL=5分钟
	if err := l.qrLoginService.SaveUUIDMapping(ctx, newUUID, userID); err != nil {
		return nil, err
	}

	// 构造二维码内容：只包含 game_id 和 uuid，不暴露 user_id
	content := fmt.Sprintf("{\"type\":\"qr_login\",\"game_id\":\"%s\",\"uuid\":\"%s\"}", req.GameID, newUUID)

	// 生成二维码PNG字节
	png, err := qrcode.Encode(content, qrcode.Medium, 256)
	if err != nil {
		return nil, err
	}

	// base64编码
	base64Image := base64.StdEncoding.EncodeToString(png)

	return &bean.GenerateQRLoginResp{
		QRBase64: base64Image,
	}, nil
}



// PlayerBind 玩家扫码绑定
// 前端传入 Base64 的二维码PNG或直接明文内容，解析后直接通过UUID校验
func (l *QRLoginLogic) PlayerBind(ctx context.Context, req *bean.PlayerBindReq) (*bean.PlayerBindResp, error) {
	// 直接处理明文JSON字符串，期望内容形如 {"type":"qr_login","game_id":"...","uuid":"..."}（不再包含user_id）
	var payload struct {
		Type   string `json:"type"`
		GameID string `json:"game_id"`
		UUID   string `json:"uuid"`
	}
	if err := json.Unmarshal([]byte(req.QRBase64Content), &payload); err != nil {
		return nil, constants.ErrQRLoginUUIDInvalid
	}
	if payload.Type != "qr_login" || payload.GameID == "" || payload.UUID == "" {
		return nil, constants.ErrQRLoginUUIDInvalid
	}
	if payload.GameID != req.GameID {
		return nil, constants.ErrQRLoginUUIDInvalid
	}

	// 第一步：通过隐藏映射查找二维码生成者的UserID，验证UUID的有效性
	qrUserID, err := l.qrLoginService.GetUserIDByUUID(ctx, payload.UUID)
	if err != nil {
		return nil, err
	}
	if qrUserID == "" {
		return nil, constants.ErrQRLoginUUIDInvalid
	}

	// 第二步：双重验证 - 通过原始UUID映射验证一致性
	originalUserID, ok, err := l.qrLoginService.ValidateUUID(ctx, payload.UUID)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, constants.ErrQRLoginUUIDInvalid
	}

	// 第三步：确保两个映射的一致性（隐藏映射和原始映射应该指向同一个用户）
	if qrUserID != originalUserID {
		logger.Logger.ErrorfCtx(ctx, "[QRLogin] UUID mapping inconsistency: hidden_mapping=%s, original_mapping=%s, uuid=%s",
			qrUserID, originalUserID, payload.UUID)
		return nil, constants.ErrQRLoginUUIDInvalid
	}

	// 记录绑定操作日志
	logger.Logger.InfofCtx(ctx, "[QRLogin] player bind: player_id=%s binding to qr_owner=%s, uuid=%s",
		req.UserID, qrUserID, payload.UUID)

	// 第四步：构造绑定信息，包含完整的关联数据
	// 注意：player_user_id 是扫码玩家的ID（用于生成登录token）
	//      qr_owner_user_id 是二维码生成者的ID（用于验证和日志追踪）
	bind := map[string]string{
		"game_id":          req.GameID,
		"player_user_id":   req.UserID, // 扫码玩家的user_id（用于生成token）
		"qr_owner_user_id": qrUserID,   // 二维码生成者的user_id（用于验证）
		"uuid":             payload.UUID,
		"bind_timestamp":   fmt.Sprintf("%d", time.Now().Unix()), // 绑定时间戳
	}

	// 第五步：序列化并保存绑定记录
	bindJSON, err := json.Marshal(bind)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[QRLogin] failed to marshal bind record: %v", err)
		return nil, constants.ErrQRLoginUUIDInvalid
	}

	if err := l.qrLoginService.SaveBindRecord(ctx, payload.UUID, string(bindJSON)); err != nil {
		logger.Logger.ErrorfCtx(ctx, "[QRLogin] failed to save bind record: uuid=%s, error=%v", payload.UUID, err)
		return nil, err
	}

	logger.Logger.InfofCtx(ctx, "[QRLogin] bind success: player=%s bound to qr_owner=%s via uuid=%s",
		req.UserID, qrUserID, payload.UUID)

	return &bean.PlayerBindResp{Status: "success"}, nil
}

// CheckLoginStatus 主播端查询绑定并生成玩家JWT
func (l *QRLoginLogic) CheckLoginStatus(ctx context.Context, req *bean.CheckLoginStatusReq) (*bean.CheckLoginStatusResp, error) {
	userID := req.UserID
	if userID == "" {
		logger.Logger.ErrorfCtx(ctx, "[QRLogin] check login status failed: missing user_id")
		return nil, constants.ErrAuthFailed
	}

	ctxName := "default"
	logger.Logger.InfofCtx(ctx, "[QRLogin] checking login status for user: %s", userID)

	// 第一步：获取主播当前激活的UUID
	uuid, err := l.qrLoginService.GetActiveUUID(ctx, userID, ctxName)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[QRLogin] failed to get active UUID for user %s: %v", userID, err)
		return nil, err
	}
	if uuid == "" {
		logger.Logger.InfofCtx(ctx, "[QRLogin] no active UUID found for user %s, status: expired", userID)
		return &bean.CheckLoginStatusResp{Status: "expired"}, nil
	}

	// 第二步：双重验证UUID的有效性（参考PlayerBind的验证逻辑）
	// 通过隐藏映射验证UUID对应的用户ID
	mappedUserID, err := l.qrLoginService.GetUserIDByUUID(ctx, uuid)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[QRLogin] failed to get mapped user ID for uuid %s: %v", uuid, err)
		return nil, err
	}
	if mappedUserID == "" {
		logger.Logger.InfofCtx(ctx, "[QRLogin] UUID %s has no mapped user ID, status: expired", uuid)
		return &bean.CheckLoginStatusResp{Status: "expired"}, nil
	}

	// 第三步：验证映射一致性（确保隐藏映射和原始映射指向同一用户）
	originalUserID, validUUID, err := l.qrLoginService.ValidateUUID(ctx, uuid)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[QRLogin] failed to validate UUID %s: %v", uuid, err)
		return nil, err
	}
	if !validUUID {
		logger.Logger.InfofCtx(ctx, "[QRLogin] UUID %s is invalid, status: expired", uuid)
		return &bean.CheckLoginStatusResp{Status: "expired"}, nil
	}

	// 第四步：确保映射一致性（参考PlayerBind的一致性检查）
	if mappedUserID != originalUserID || originalUserID != userID {
		logger.Logger.ErrorfCtx(ctx, "[QRLogin] UUID mapping inconsistency: mapped=%s, original=%s, expected=%s, uuid=%s",
			mappedUserID, originalUserID, userID, uuid)
		return &bean.CheckLoginStatusResp{Status: "expired"}, nil
	}

	// 第五步：查询绑定记录（使用原子 get+del，避免并发重复发放）
	bindJSON, err := l.qrLoginService.GetAndDeleteBindRecord(ctx, uuid)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[QRLogin] failed to get bind record for uuid %s: %v", uuid, err)
		return nil, err
	}

	if bindJSON == "" {
		// 无绑定记录，返回等待状态
		logger.Logger.InfofCtx(ctx, "[QRLogin] no bind record found for uuid %s, status: pending", uuid)
		return &bean.CheckLoginStatusResp{Status: "pending"}, nil
	}
	// 第六步：解析绑定记录，验证数据完整性
	var bind struct {
		GameID        string `json:"game_id"`
		PlayerUserID  string `json:"player_user_id"`   // 新格式：扫码玩家的user_id
		QROwnerUserID string `json:"qr_owner_user_id"` // 新格式：二维码生成者的user_id
		UUID          string `json:"uuid"`
		BindTimestamp string `json:"bind_timestamp"` // 绑定时间戳

		// 兼容旧格式
		UserID string `json:"user_id"` // 旧格式的user_id字段
	}

	if err := json.Unmarshal([]byte(bindJSON), &bind); err != nil {
		logger.Logger.ErrorfCtx(ctx, "[QRLogin] failed to unmarshal bind record for uuid %s: %v", uuid, err)
		return nil, constants.ErrQRLoginUUIDInvalid
	}

	// 第七步：验证绑定记录的完整性和一致性
	// 确定扫码玩家的用户ID（优先使用新格式，向后兼容旧格式）
	playerUserID := bind.PlayerUserID
	if playerUserID == "" {
		playerUserID = bind.UserID // 向后兼容旧格式
	}

	if playerUserID == "" {
		logger.Logger.ErrorfCtx(ctx, "[QRLogin] no valid player user ID in bind record for uuid %s", uuid)
		return nil, constants.ErrQRLoginUUIDInvalid
	}

	// 验证绑定记录中的UUID与当前UUID一致
	if bind.UUID != "" && bind.UUID != uuid {
		logger.Logger.ErrorfCtx(ctx, "[QRLogin] UUID mismatch in bind record: expected=%s, got=%s", uuid, bind.UUID)
		return nil, constants.ErrQRLoginUUIDInvalid
	}

	// 验证二维码生成者的用户ID与当前用户一致
	if bind.QROwnerUserID != "" && bind.QROwnerUserID != userID {
		logger.Logger.ErrorfCtx(ctx, "[QRLogin] QR owner mismatch: expected=%s, got=%s", userID, bind.QROwnerUserID)
		return nil, constants.ErrQRLoginUUIDInvalid
	}

	// 第八步：生成玩家登录token
	// 获取玩家的openID（从a_user_douyin表）
	playerOpenID := ""
	douyinUserInfo, err := l.userService.GetUserDouyinModel(ctx, playerUserID)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "[QRLogin] failed to get douyin user info for player %s: %v", playerUserID, err)
	} else if douyinUserInfo != nil {
		playerOpenID = douyinUserInfo.OpenID
	}

	// 获取app_id（从a_config_douyin表）
	appID := ""
	douyinConfig, err := l.douyinService.GetDouyinConf(ctx, bind.GameID)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "[QRLogin] failed to get douyin config for game %s: %v", bind.GameID, err)
	} else if douyinConfig != nil {
		appID = douyinConfig.AppID
	}

	token, err := util.GenerateToken(playerUserID, constants.DeviceIDStreamerProxyPaymentProhibited, appID, bind.GameID, playerOpenID, "")
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[QRLogin] failed to generate token for player %s: %v", playerUserID, err)
		return nil, err
	}

	// 第九步：记录成功日志并返回结果
	logger.Logger.InfofCtx(ctx, "[QRLogin] login success: qr_owner=%s, player=%s, uuid=%s, bind_timestamp=%s",
		userID, playerUserID, uuid, bind.BindTimestamp)

	return &bean.CheckLoginStatusResp{Status: "success", PlayerToken: token}, nil
}
