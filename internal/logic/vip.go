package logic

import (
	"context"
	"sync"
	"time"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
)

var (
	_vipLogicOnce sync.Once
	_vipLogic     *VIPLogic
)

type VIPLogic struct {
	vipService *service.VIPService
}

func SingletonVIPLogic() *VIPLogic {
	_vipLogicOnce.Do(func() {
		_vipLogic = &VIPLogic{
			vipService: service.SingletonVIPService(),
		}
	})
	return _vipLogic
}

// CheckVIPPopup 检查VIP弹窗（游戏端接口，使用JWT认证）
func (l *VIPLogic) CheckVIPPopup(ctx context.Context, req *bean.CheckVIPPopupReq) (*bean.CheckVIPPopupResp, error) {
	// 从JWT Header中获取UserID和GameID
	userID := req.UserID
	gameID := req.GameID

	logger.Logger.InfofCtx(ctx, "CheckVIPPopup called with UserID: %s, GameID: %s from JWT", userID, gameID)

	// 首先验证游戏ID是否存在
	gameExists, err := l.vipService.CheckGameExists(ctx, gameID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "Failed to check game exists: %v", err)
		return &bean.CheckVIPPopupResp{
			ShouldPopup: false,
		}, nil
	}

	if !gameExists {
		logger.Logger.WarnfCtx(ctx, "Game not found: %s", gameID)
		return &bean.CheckVIPPopupResp{
			ShouldPopup: false,
		}, nil
	}

	// 先检查是否存在任意已认证的VIP记录（只使用user_id查询）
	hasAuthenticatedVIP, err := l.vipService.HasAnyAuthenticatedVIPByUserID(ctx, userID)
	if err != nil {
		logger.Logger.InfofCtx(ctx, "Failed to check authenticated VIP: %v", err)
		return &bean.CheckVIPPopupResp{
			ShouldPopup: false,
		}, nil
	}
	if hasAuthenticatedVIP {
		// 任一VIP记录已认证，不弹窗
		return &bean.CheckVIPPopupResp{
			ShouldPopup: false,
		}, nil
	}

	// 若无已认证记录，再判断该用户是否属于VIP，如不是，不弹窗
	vipUser, err := l.vipService.GetVIPUserByUserID(ctx, userID)
	if err != nil {
		logger.Logger.InfofCtx(ctx, "Failed to get VIP user: %v", err)
		return &bean.CheckVIPPopupResp{
			ShouldPopup: false,
		}, nil
	}
	if vipUser == nil {
		return &bean.CheckVIPPopupResp{
			ShouldPopup: false,
		}, nil
	}

	// 检查弹窗规则是否开启（从m_vip_assignment_rule查询）
	enabled, err := l.vipService.IsVIPPopupEnabled(ctx, gameID)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "Failed to query popup rule: %v", err)
		return &bean.CheckVIPPopupResp{ShouldPopup: false}, nil
	}
	if !enabled {
		return &bean.CheckVIPPopupResp{ShouldPopup: false}, nil
	}

	// 检查今日是否已弹窗（按用户聚合，存在多条记录时任意命中即视为已弹）
	today := time.Now().In(time.Local).Format("2006-01-02")
	hasPopupToday, err := l.vipService.HasAnyPopupTodayByUserID(ctx, userID, today)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "Failed to check popup today: %v", err)
	}
	if hasPopupToday {
		// 今日已弹窗
		return &bean.CheckVIPPopupResp{
			ShouldPopup: false,
		}, nil
	}

	// 记录弹窗日志（只使用user_id）
	err = l.vipService.RecordPopupByUserID(ctx, userID, today)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "Failed to record popup: %v", err)
	}

	// 获取弹窗图片URL（同表查询）
	popupImageURL, err := l.vipService.GetVIPPopupImageURL(ctx, gameID)
	if err != nil {
		logger.Logger.WarnfCtx(ctx, "Failed to get popup image URL: %v", err)
		popupImageURL = ""
	}

	return &bean.CheckVIPPopupResp{
		ShouldPopup:     true,
		WechatQrCodeURL: popupImageURL,
	}, nil
}
