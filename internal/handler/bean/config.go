package bean

import "git.panlonggame.com/bkxplatform/admin-console/internal/middleware"

type GetGameConfigReq struct {
	GameID string `form:"game_id"`
}

type GetGameConfigRes struct {
	GameName           string `json:"game_name"`
	GravityAccessToken string `json:"gravity_access_token"`
	GravityIsEnabled   int32  `json:"gravity_is_enabled"`
	PlatformAppID      string `json:"platform_app_id"` // 数数科技ID
	QiyuWechatAppID    string `json:"qiyu_wechat_app_id"`
	PayMethod          int32  `json:"pay_method"`
	AppID              string `json:"app_id"`

	TencentDataSourceID  string `json:"tencent_data_source_id"`
	TencentEncryptionKey string `json:"tencent_encryption_key"`
	TencentAdCycle       int32  `json:"tencent_ad_cycle"`
}

// GetSwitchesReq 获取自定义开关
type GetSwitchesReq struct {
	GameID       string   `json:"game_id" binding:"required"`
	SwitchesIDs  []string `json:"switches_ids"`
	Version      string   `json:"version" binding:"required"`
	PlatformType string   `json:"platform_type" binding:"required"` // minigame | douyin_minigame | alipay_minigame
}

// GetSwitchesV2Req 获取自定义开关
type GetSwitchesV2Req struct {
	middleware.Header // 自动绑定，无需传入，用户id, jwt 获取到的用户id

	RequestGameID string   `json:"game_id" binding:"required"`
	SwitchesIDs   []string `json:"switches_ids"`
	Version       string   `json:"version" binding:"required"`
	PlatformType  string   `json:"platform_type" binding:"required"` // minigame | douyin_minigame | alipay_minigame

	NickName   string                   `json:"nick_name"`
	Scene      string                   `json:"scene"`
	Params     []map[string]interface{} `json:"params"`
	IP         string                   `json:"ip"`
	IPRegionID string                   `json:"ip_region_id"` // 解析生成
	Channel    string                   `json:"channel"`
	// UniqueID   string            `json:"unique_id"` // 平台的user_id等同
}

type SwitchClientParam map[string]interface{}

type GetSwitchesResp struct {
	Switches []interface{} `json:"switches"`
}

type SwitchValue struct {
	Versions            string `json:"versions"`
	UpdatedAt           string `json:"updated_at"`
	ID                  string `json:"id"`
	SwitchID            string `json:"switch_id"`
	ApplicablePlatforms string `json:"applicable_platforms"`
	EffectiveTimeEnd    string `json:"effective_time_end"`
	DefaultReturn       string `json:"default_return"`
	Status              string `json:"status"`
	CreatedAt           string `json:"created_at"`
	GameID              string `json:"game_id"`
	Title               string `json:"title"`
	EffectiveTimeStart  string `json:"effective_time_start"`
}

// GetAdReq 获取广告位配置请求
type GetAdReq struct {
	GameID       string `form:"game_id"`       // 游戏ID
	PlatformType string `form:"platform_type"` // 平台类型
	PositionID   string `form:"position_id"`   // 广告位ID
}

// GetAdResp 获取广告位配置响应
type GetAdResp struct {
	PlatformCode string `json:"platform_code"` // 平台广告位
}
