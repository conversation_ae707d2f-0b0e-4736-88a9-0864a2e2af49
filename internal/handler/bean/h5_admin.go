package bean

// H5AdminUser H5管理系统用户模型
type H5AdminUser struct {
	UserID         string `json:"user_id"`
	Username       string `json:"username"`
	IsRealNameAuth bool   `json:"is_real_name_auth"`
	IsMinors       bool   `json:"is_minors"`
	CreatedAt      int64  `json:"created_at"`
	UpdatedAt      int64  `json:"updated_at"`
	// BirthDate      int64  `json:"birth_date"` // 用户出生日期
	Age int `json:"age"` // 用户年龄
}

// H5AdminUserRechargeLimit H5用户充值额度限制
type H5AdminUserRechargeLimit struct {
	ID          int64   `json:"id"`           // 主键
	UserID      string  `json:"user_id"`      // 用户唯一标识
	YearMonth   string  `json:"year_month"`   // 年月，格式YYYYMM
	TotalAmount float64 `json:"total_amount"` // 当月累计充值金额
	UpdatedAt   int64   `json:"updated_at"`   // 最后更新时间
}

// RegisterRequest 注册请求参数
type RegisterRequest struct {
	Username string `json:"username" form:"username" binding:"required,min=1,max=50"` // 用户名长度3-50
	Password string `json:"password" form:"password" binding:"required,min=6,max=12"` // 密码最小长度6
}

// RegisterResponse 注册响应
type RegisterResponse struct {
	UserID         string `json:"user_id"`
	Username       string `json:"username"`
	IsRealNameAuth bool   `json:"is_real_name_auth"`
	IsMinors       bool   `json:"is_minors"`
	CreatedAt      int64  `json:"created_at"`
	UpdatedAt      int64  `json:"updated_at"`
}

// LoginRequest 登录请求参数
type LoginRequest struct {
	Username string `json:"username" form:"username" binding:"required"` // 用户名
	Password string `json:"password" form:"password" binding:"required"` // 密码
}

// LoginResponse 登录响应
type LoginResponse struct {
	User         H5AdminUser `json:"user" form:"user"`
	IsPlayable   bool        `json:"is_playable"`   // 今天是否为可玩日期
}

// TimestampResp 时间戳响应
type TimestampResp struct {
	Timestamp int64 `json:"timestamp"` // 服务器当前时间戳(毫秒)
}

// TimestampRequest 时间戳请求
type TimestampRequest struct {
	IsSpecifyTime bool  `json:"is_specify_time" form:"is_specify_time"` // 是否指定时间
	TimestampDiff int64 `json:"timestamp_diff" form:"timestamp_diff"`   // 时间差(秒)
}

// RechargeCheckRequest 充值额度检查请求参数
type RechargeCheckRequest struct {
	Username       string  `json:"username" binding:"required"`             // 用户名
	RechargeAmount float64 `json:"recharge_amount" binding:"required,gt=0"` // 充值金额
}

// RechargeCheckResponse 充值额度检查响应
type RechargeCheckResponse struct {
	AllowRecharge bool   `json:"allow_recharge"` // 是否允许充值
	ShowPopup     bool   `json:"show_popup"`     // 是否显示弹窗
	PopupMessage  string `json:"popup_message"`  // 弹窗信息，仅当show_popup为true时有效
}

// RechargePopupRequest 充值弹窗判断请求参数
type RechargePopupRequest struct {
	Username string `json:"username" binding:"required"` // 用户名
}

// RechargePopupResponse 充值弹窗判断响应
type RechargePopupResponse struct {
	ShowPopup bool   `json:"show_popup"` // 是否弹窗
	Message   string `json:"message"`    // 弹窗文案
}

// RealNameVerifyRequest 实名认证请求参数
type RealNameVerifyRequest struct {
	UserID   string `json:"user_id" binding:"required"`    // 用户ID
	RealName string `json:"real_name" binding:"required"`  // 真实姓名
	IDCardNo string `json:"id_card_no" binding:"required"` // 身份证号
}

// RealNameVerifyResponse 实名认证响应
type RealNameVerifyResponse struct {
	UserID         string `json:"user_id"`           // 用户ID
	IsRealNameAuth bool   `json:"is_real_name_auth"` // 是否实名认证
	IsMinors       bool   `json:"is_minors"`         // 是否未成年
	BirthDate      int64  `json:"birth_date"`        // 出生日期时间戳
	Age            int    `json:"age"`               // 年龄
}
