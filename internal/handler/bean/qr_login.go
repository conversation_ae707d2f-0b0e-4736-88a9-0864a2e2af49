package bean

import (
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
)

// GenerateQRLoginReq 生成扫码登录二维码请求
type GenerateQRLoginReq struct {
	middleware.Header
}

type GenerateQRLoginResp struct {
	QRBase64 string `json:"qr_base64"`
}

// PlayerBindReq 玩家扫码绑定请求（需要JWT）
// 前端传递 game_id 与 QRBase64Content（二维码Base64图片内容或明文内容）
type PlayerBindReq struct {
	middleware.Header
	QRBase64Content string `json:"qr_base64_content" binding:"required"`
}

// PlayerBindResp 绑定结果
type PlayerBindResp struct {
	Status string `json:"status"` // success
}

// CheckLoginStatusReq 主播端查询登录状态请求
type CheckLoginStatusReq struct {
	middleware.Header
}

// CheckLoginStatusResp 查询结果
type CheckLoginStatusResp struct {
	Status      string `json:"status"`       // pending / expired / success
	PlayerToken string `json:"player_token"` // success时返回
}
