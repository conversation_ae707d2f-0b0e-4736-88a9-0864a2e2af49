package bean

type VerifySensitiveMessageReq struct {
	UserID       string `json:"user_id" binding:"required"`
	GameID       string `json:"game_id" binding:"required"`
	PlatformType string `json:"platform_type"` // minigame: 微信小游戏; douyin_minigame: 抖音小游戏; alipay_minigame: 支付宝小游戏
	Msg          string `json:"msg"`
}

type VerifySensitiveMessageRes struct {
	// Result  WechatSecurityCheckResult    `json:"result"`   // 综合结果
	// Detail  []*WechatSecurityCheckDetail `json:"detail"`   // 结果明细
	// TraceID string                       `json:"trace_id"` // 微信返回的请求标识
	TraceID         string                            `json:"trace_id"`
	Source          string                            `json:"source"` // weixin or douyin
	ErrCode         int32                             `json:"err_code"`
	ErrMsg          string                            `json:"err_msg"`
	Result          WechatSecurityCheckResult         `json:"result"`
	Detail          interface{}                       `json:"detail"`
	PlatformDetail  []*SensitiveMessagePlatformDetail `json:"platform_detail"`
	ReplacedContent string                            `json:"replaced_content"`
}

type SensitiveMessagePlatformDetail struct {
	Level   int32  `json:"level"`
	Keyword string `json:"keyword"`
}

type WechatSecurityCheck struct {
	MinigameErr
	Result  WechatSecurityCheckResult    `json:"result"`   // 综合结果
	Detail  []*WechatSecurityCheckDetail `json:"detail"`   // 结果明细
	TraceID string                       `json:"trace_id"` // 微信返回的请求标识
}

type WechatSecurityCheckResult struct {
	Suggest         string `json:"suggest"`
	Label           int    `json:"label"`
	ReplacedContent string `json:"replaced_content"`
}

type WechatSecurityCheckDetail struct {
	Strategy string `json:"strategy"`
	ErrCode  int    `json:"errcode"`
	Suggest  string `json:"suggest"`
	Label    int    `json:"label"`
	Prob     int    `json:"prob"`
	Level    int    `json:"level"`
	Keyword  string `json:"keyword"`
}

// DouyinSecurityCheckDetail 抖音安全检查详情
type DouyinSecurityCheckDetail struct {
	LogID string               `json:"log_id"`
	Data  []DouyinSecurityData `json:"data"`
}

// DouyinSecurityData 抖音安全检查数据
type DouyinSecurityData struct {
	Code     int32                   `json:"code"`
	TaskID   string                  `json:"task_id"`
	DataID   interface{}             `json:"data_id"`
	Cached   bool                    `json:"cached"`
	Predicts []DouyinSecurityPredict `json:"predicts"`
	Msg      string                  `json:"msg"`
}

// DouyinSecurityPredict 抖音安全检查预测结果
type DouyinSecurityPredict struct {
	Prob      int    `json:"prob"`
	Hit       bool   `json:"hit"`
	ModelName string `json:"model_name"`
	Target    string `json:"target"`
}

// AlipayContentCheckResult 支付宝内容检测标准化结构
type AlipayContentCheckResult struct {
	Suggestion        string                   `json:"suggestion"` // REJECT|REVIEW|PASS
	Keywords          []string                 `json:"keywords"`   // 检测到的敏感词
	RiskLevel         string                   `json:"risk_level"` // HIGH|MEDIUM|LOW
	DetectCheckLabels *AlipayDetectCheckLabels `json:"detect_check_labels"`
	RequestID         string                   `json:"request_id"` // 支付宝请求ID，用于设置TraceID
}

// AlipayDetectCheckLabels 支付宝检测标签信息（用于原样写入 detail）
type AlipayDetectCheckLabels struct {
	Label          string                 `json:"label"`
	Rate           string                 `json:"rate"`
	SubCheckLabels []*AlipaySubCheckLabel `json:"sub_check_labels"`
}

type AlipaySubCheckLabel struct {
	HitStrategy int    `json:"hit_strategy"`
	SubLabel    string `json:"sub_label"`
	Rate        string `json:"rate"`
}
