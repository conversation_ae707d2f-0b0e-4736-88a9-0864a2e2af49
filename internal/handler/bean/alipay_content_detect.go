package bean

// AlipayContentDetectReq 对齐官方Java示例的可选参数集合
// 注意：data_list 与 content 二选一；Products 以逗号拼接传入支付宝
// Channel 建议使用 "tinyapp-eco-open"；ContentType 可为 TEXT/PICTURE 等
// Scene 用于内部标识（如原实现中的 content_detect），若支付宝不识别会忽略
// AppAuthToken 用于三方代调用场景，SDK当前可能无法直接附加到顶层参数
//
// 字段命名遵循本项目 bean 风格（驼峰），序列化在 service 层完成
// 仅作承载，不直接用于绑定HTTP请求
type AlipayContentDetectReq struct {
	RequestID   string   // request_id
	Tenants     string   // tenants，如 TAN1234
	Products    []string // products，示例：{"TJ_POLITICS_MC","TJ_PORN_MC"}
	Channel     string   // channel，示例：tinyapp-eco-open
	ContentType string   // content_type：TEXT/PICTURE
	// Content      string   // 文本内容
	DataList     []string // 内容列表（如图片URL/文本列表）
	OpenID       string   // open_id
	UserID       string   // user_id（将废弃）
	Scene        string   // 透传的业务场景标识
	AppAuthToken string   // 第三方代调用的 app_auth_token
}
