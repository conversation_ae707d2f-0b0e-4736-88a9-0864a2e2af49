package bean

import "git.panlonggame.com/bkxplatform/admin-console/internal/middleware"

// 游戏端VIP接口（简化版，使用JWT认证）

// CheckVIPPopupReq 检查VIP弹窗请求
type CheckVIPPopupReq struct {
	middleware.Header
	// UserID和GameID将从JWT token中获取，不再通过请求参数传递
	// PlatformType string `json:"platform_type" binding:"required"`
}

// CheckVIPPopupResp 检查VIP弹窗响应
type CheckVIPPopupResp struct {
	ShouldPopup     bool   `json:"should_popup"`       // 是否应该弹窗
	WechatQrCodeURL string `json:"wechat_qr_code_url"` // 微信二维码URL
}

// 已废弃：GetVIPStatusReq / GetVIPStatusResp
