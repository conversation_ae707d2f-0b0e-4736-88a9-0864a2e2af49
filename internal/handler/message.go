package handler

import (
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/logic"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

var (
	_messageOnce    sync.Once
	_messageHandler *MessageHandler
)

type MessageHandler struct {
	middleware.BaseHandler
	messageLogic *logic.MessageLogic
}

func SingletonMessageHandler() *MessageHandler {
	_messageOnce.Do(func() {
		_messageHandler = &MessageHandler{
			messageLogic: logic.SingletonMessageLogic(),
		}
	})
	return _messageHandler
}

// SubMessageNotify 订阅消息
func (h *MessageHandler) SubMessageNotify(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.SubMessageNotifyReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.messageLogic.SubMessageNotify(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// DouyinSubMessageNotify 抖音订阅消息
func (h *MessageHandler) DouyinSubMessageNotify(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.DouyinSubMessageNotifyReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.messageLogic.DouyinSubMessageNotify(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// AlipaySubMessageNotify 支付宝订阅消息
func (h *MessageHandler) AlipaySubMessageNotify(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.AlipaySubMessageNotifyReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.messageLogic.AlipaySubMessageNotify(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}
