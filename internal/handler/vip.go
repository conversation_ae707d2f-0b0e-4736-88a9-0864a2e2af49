package handler

import (
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/logic"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

var (
	_vipOnce    sync.Once
	_vipHandler *VIPHandler
)

type VIPHandler struct {
	middleware.BaseHandler
	vipLogic *logic.VIPLogic
}

func SingletonVIPHandler() *VIPHandler {
	_vipOnce.Do(func() {
		_vipHandler = &VIPHandler{
			vipLogic: logic.SingletonVIPLogic(),
		}
	})
	return _vipHandler
}

// CheckVIPPopup 检查VIP弹窗（游戏端接口，使用JWT认证）
func (h *VIPHandler) CheckVIPPopup(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.CheckVIPPopupReq{}
	if !h.Bind(c, req) {
		return
	}

	resp, err := h.vipLogic.CheckVIPPopup(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}
