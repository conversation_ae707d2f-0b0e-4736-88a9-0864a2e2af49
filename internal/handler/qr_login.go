package handler

import (
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/logic"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

var (
	_qrLoginOnce    sync.Once
	_qrLoginHandler *QRLoginHandler
)

type QRLoginHandler struct {
	middleware.BaseHandler
	logic *logic.QRLoginLogic
}

func SingletonQRLoginHandler() *QRLoginHandler {
	_qrLoginOnce.Do(func() {
		_qrLoginHandler = &QRLoginHandler{logic: logic.SingletonQRLoginLogic()}
	})
	return _qrLoginHandler
}

// GenerateLoginQRCode 生成二维码（需要JWT）
func (h *QRLoginHandler) GenerateLoginQRCode(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.GenerateQRLoginReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.logic.GenerateUUIDQRCode(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// BindPlayerInfo 玩家扫码绑定（需要JWT）
func (h *QRLoginHandler) BindPlayerInfo(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.PlayerBindReq{}
	if !h.Bind(c, req) { // 需要绑定头(JWT)
		return
	}
	resp, err := h.logic.PlayerBind(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}

// CheckLoginStatus 主播查询扫码登录状态（需要JWT）
func (h *QRLoginHandler) CheckLoginStatus(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.CheckLoginStatusReq{}
	if !h.Bind(c, req) {
		return
	}
	resp, err := h.logic.CheckLoginStatus(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}
	h.Success(c, resp)
}
