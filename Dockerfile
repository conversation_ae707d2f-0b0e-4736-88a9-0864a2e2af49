# 使用官方 Golang 镜像创建构建产物。
# 基于 CentOS，将 GOPATH 设置为 /go。
FROM bkxplatform.tencentcloudcr.com/public/golang:1.22.7 as builder

# 将本地项目文件复制到容器的工作目录。
ADD . /go/src/admin-console

# 在容器内部构建项目。
# （您可以在此处手动获取或管理依赖项，
# 或使用类似 "godep" 的工具。）
ENV GOPROXY=https://goproxy.cn,direct
ENV GOCACHE=/root/.cache/go-build
RUN --mount=type=cache,target="/root/.cache/go-build" cd /go/src/admin-console && go build -o /admin-console

# 使用 Docker 多阶段构建创建精简的生产镜像。
FROM bkxplatform.tencentcloudcr.com/public/debian:latest
# 替换阿里云加速源
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources
RUN apt-get update && \
    apt-get install -y --no-install-recommends ca-certificates && \
    rm -rf /var/lib/apt/lists/*
ENV Env=local

# 从构建阶段复制二进制文件到生产镜像。
COPY --from=builder /admin-console /admin-console

# 从构建阶段复制配置文件到生产镜像。
COPY --from=builder /go/src/admin-console/configs /configs
COPY --from=builder /go/src/admin-console/static /static

# 容器启动时默认运行 admin-console 命令。
ENTRYPOINT ["/admin-console"]

# 文档说明服务监听 10100 端口。
EXPOSE 10000

#docker build -t admin-console .
#docker run -p 10100:10100 admin-console
