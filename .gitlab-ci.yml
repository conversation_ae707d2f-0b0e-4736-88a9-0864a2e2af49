stages:
  - .pre
  - build
  - push
  - deploy_dev
  - deploy_test
  - deploy_prod



###
# 不同项目需要修改的信息
# 1. variables 变量列表
# 2. .job_config 里的 tags
# 3. build 阶段的 docker build 命令
###  



variables:
  DOCKER_IMAGE_DEV: "bkxplatform.tencentcloudcr.com/platform/admin-console-dev"  
  DOCKER_IMAGE_TEST: "bkxplatform.tencentcloudcr.com/platform/admin-console-test"  
  DOCKER_IMAGE_PROD: "bkxplatform.tencentcloudcr.com/platform/admin-console-prod"  
  KUBE_NAMESPACE_DEV: "platform-dev"
  KUBE_NAMESPACE_TEST: "platform-test"
  KUBE_NAMESPACE_PROD: "platform-prod"
  SERVICE_LIST_DEV: "admin-console-dev"
  SERVICE_LIST_TEST: "admin-console-test"
  SERVICE_LIST_PROD: "admin-console-prod"
  BOT_SEND_ADDR: "http://ops-tools.bkxgame.com:8888/feishuBot/notification_send"
  BOT_UPDATE_ADDR: "http://ops-tools.bkxgame.com:8888/feishuBot/notification_update"
  BOT_CARD_ID: "AAq9m9a335irS"
  BOT_CARD_VERSION: "1.1.1"
  PROJECT_NAME: "admin-console"
  CHAT_ID: "oc_2cc19f88ce0e8f77ea541ad834f52393"



### 共享配置锚点
.job_config: &job_config
  tags:
    - platform-ops



# 发送通知锚点
.notification: &notification
  before_script:
    - |
      echo "消息ID: ${MESSAGE_ID}"

    - |
      COLOR="blue"

      json_data=$(jq -n \
        --arg message_id "$MESSAGE_ID" \
        --arg template_id "$BOT_CARD_ID" \
        --arg template_version_name "$BOT_CARD_VERSION" \
        --arg env "$ENV" \
        --arg project_name "$PROJECT_NAME" \
        --arg action "$ACTION 开始" \
        --arg branch "$BRANCH" \
        --arg commit "$CI_COMMIT_MESSAGE" \
        --arg job_url "$CI_PIPELINE_URL" \
        --arg author "$GITLAB_USER_NAME" \
        --arg tag "$IMAGE_TAG" \
        --arg color "$COLOR" \
        '{
          "message_id": $message_id,
          "template_id": $template_id,
          "template_version_name": $template_version_name,
          "env": $env,
          "project_name": $project_name,
          "action": $action,
          "branch": $branch,
          "commit": $commit,
          "job_url": $job_url,
          "author": $author,
          "tag": $tag,
          "color": $color
        }')

      curl -s -X POST ${BOT_UPDATE_ADDR} -H "Content-Type: application/json" -d "$json_data"

  after_script:
    - |
      if [ "$CI_JOB_STATUS" == "success" ];then
        COLOR="green"
        status="完成"
      else
        COLOR="red"
        status="失败"
      fi

      json_data=$(jq -n \
        --arg message_id "$MESSAGE_ID" \
        --arg template_id "$BOT_CARD_ID" \
        --arg template_version_name "$BOT_CARD_VERSION" \
        --arg env "$ENV" \
        --arg project_name "$PROJECT_NAME" \
        --arg action "$ACTION $status" \
        --arg branch "$BRANCH" \
        --arg commit "$CI_COMMIT_MESSAGE" \
        --arg job_url "$CI_PIPELINE_URL" \
        --arg author "$GITLAB_USER_NAME" \
        --arg tag "$IMAGE_TAG" \
        --arg color "$COLOR" \
        '{
          "message_id": $message_id,
          "template_id": $template_id,
          "template_version_name": $template_version_name,
          "env": $env,
          "project_name": $project_name,
          "action": $action,
          "branch": $branch,
          "commit": $commit,
          "job_url": $job_url,
          "author": $author,
          "tag": $tag,
          "color": $color
        }')

      curl -s -X POST ${BOT_UPDATE_ADDR} -H "Content-Type: application/json" -d "$json_data"



# 发布服务锚点
.deploy: &deploy

  # 执行发布
  script:
    - DEPLOYMENTS=($DEPLOYMENTS)
    - |
      for deployment in "${DEPLOYMENTS[@]}"; do
        kubectl set image deployment/$deployment $deployment=$DOCKER_IMAGE:$IMAGE_TAG -n ${NAMESPACE}
      done

      for deployment in "${DEPLOYMENTS[@]}"; do
        echo "检查部署 ${deployment} 的状态..."
        if ! kubectl rollout status deployment/$deployment -n ${NAMESPACE} --timeout=300s; then
          echo "部署 ${deployment} 更新失败或超时"
          exit 1
        fi
      done

      echo "所有服务部署更新成功!"



init:
  <<: [*job_config]
  stage: .pre  # 特殊阶段，最先执行
  script:
    # 获取时间戳
    - export TIMESTAMP=$(date +"%Y%m%d%H%M%S")

    # 定义镜像标签
    - |
      if [[ -n "$CI_COMMIT_TAG" ]]; then
        export IMAGE_TAG="${CI_COMMIT_TAG}-${TIMESTAMP}"
        export BRANCH="${CI_COMMIT_TAG}"
      else
        # 使用分支名+时间戳
        export IMAGE_TAG="${CI_COMMIT_REF_SLUG}-${TIMESTAMP}"
        export BRANCH="${CI_COMMIT_BRANCH}"
      fi

    # 定义镜像地址
    - |
      if [[ "$CI_COMMIT_BRANCH" == "develop" ]]; then
        export DOCKER_IMAGE="$DOCKER_IMAGE_DEV"
        export ENV="开发环境"
      elif [[ "$CI_COMMIT_BRANCH" == "test" ]]; then
        export DOCKER_IMAGE="$DOCKER_IMAGE_TEST"
        export ENV="测试环境"
      elif [[ "$CI_COMMIT_BRANCH" == "master" || -n "$CI_COMMIT_TAG" ]]; then
        export DOCKER_IMAGE="$DOCKER_IMAGE_PROD"
        export ENV="正式环境"
      else
        exit 1
      fi

    # init阶段 直接发送 构建镜像的通知出来，并且存储返回的 message_id
    - apt-get update && apt-get -y install jq
    - |
      json_data=$(jq -n \
        --arg chat_id "$CHAT_ID" \
        --arg template_id "$BOT_CARD_ID" \
        --arg template_version_name "$BOT_CARD_VERSION" \
        --arg env "$ENV" \
        --arg project_name "$PROJECT_NAME" \
        --arg action "构建镜像 开始" \
        --arg branch "$BRANCH" \
        --arg commit "$CI_COMMIT_MESSAGE" \
        --arg job_url "$CI_PIPELINE_URL" \
        --arg author "$GITLAB_USER_NAME" \
        --arg tag "$IMAGE_TAG" \
        --arg color "yellow" \
        '{
          "chat_id": $chat_id,
          "template_id": $template_id,
          "template_version_name": $template_version_name,
          "env": $env,
          "project_name": $project_name,
          "action": $action,
          "branch": $branch,
          "commit": $commit,
          "job_url": $job_url,
          "author": $author,
          "tag": $tag,
          "color": $color
        }')

      export MESSAGE_ID=`curl -s -X POST ${BOT_SEND_ADDR} -H "Content-Type: application/json" -d "${json_data}"`
    # 存储变量    
    - echo "IMAGE_TAG=$IMAGE_TAG" > info.env
    - echo "DOCKER_IMAGE=$DOCKER_IMAGE" >> info.env
    - echo "TIMESTAMP=$TIMESTAMP" >> info.env
    - echo "ENV=$ENV" >> info.env
    - echo "BRANCH=$BRANCH" >> info.env
    - echo "TIMESTAMP=$TIMESTAMP" >> info.env
    - echo "MESSAGE_ID=$MESSAGE_ID" >> info.env

  artifacts:
    reports:
      dotenv: info.env 



# 构建镜像
build:
  <<: [*job_config, *notification]
  stage: build
  needs: ["init"]

  script:
    - docker buildx build -t $DOCKER_IMAGE:$IMAGE_TAG .

  variables:
    ACTION: "构建镜像"

  rules:
    - if: ($CI_COMMIT_BRANCH == "master" || $CI_COMMIT_TAG) && $CI_PIPELINE_SOURCE == "web"



# 推送镜像（依赖构建）
push:
  <<: [*job_config, *notification]
  stage: push
  needs: ["init", "build"]

  script:
    - docker push $DOCKER_IMAGE:$IMAGE_TAG

  variables:
    ACTION: "推送镜像"
    
  rules:
    - if: ($CI_COMMIT_BRANCH == "master" || $CI_COMMIT_TAG) && $CI_PIPELINE_SOURCE == "web"



# 开发环境部署
deploy_dev:
  <<: [*job_config, *notification, *deploy]
  stage: deploy_dev
  needs: ["init", "push"]

  variables:
    DEPLOYMENTS: $SERVICE_LIST_DEV
    NAMESPACE: $KUBE_NAMESPACE_DEV
    ACTION: "更新服务"

  rules:
    - if: $CI_COMMIT_BRANCH == "develop"



# 测试环境部署
deploy_test:
  <<: [*job_config, *notification, *deploy]
  stage: deploy_test
  needs: ["init", "push"]

  variables:
    DEPLOYMENTS: $SERVICE_LIST_TEST
    NAMESPACE: $KUBE_NAMESPACE_TEST
    ACTION: "更新服务"

  rules:
    - if: $CI_COMMIT_BRANCH == "test" 



# 正式环境部署
deploy_prod:
  <<: [*job_config, *notification, *deploy]
  stage: deploy_prod
  needs: ["init", "push"]

  variables:
    DEPLOYMENTS: $SERVICE_LIST_PROD
    NAMESPACE: $KUBE_NAMESPACE_PROD
    ACTION: "更新服务"

  rules:
    - if: ($CI_COMMIT_BRANCH == "master" || $CI_COMMIT_TAG) && $CI_PIPELINE_SOURCE == "web"
